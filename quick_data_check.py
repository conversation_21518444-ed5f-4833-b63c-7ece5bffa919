import pandas as pd
import os

def check_data_integrity():
    data_dir = "results/processed_data"
    features_path = os.path.join(data_dir, "features.csv")
    products_path = os.path.join(data_dir, "products.csv")

    if not os.path.exists(features_path) or not os.path.exists(products_path):
        print("Data files not found.")
        return

    features_df = pd.read_csv(features_path)
    products_df = pd.read_csv(products_path)

    print(f"Number of rows in features.csv: {len(features_df)}")
    print(f"Number of rows in products.csv: {len(products_df)}")

    if len(features_df) != len(products_df):
        print("Warning: Row count mismatch between features.csv and products.csv.")

    rd_columns = [col for col in products_df.columns if col.startswith('RD')]
    if rd_columns:
        print(f"Found RD columns in products.csv: {rd_columns}")
    else:
        print("Warning: No RD columns found in products.csv.")

if __name__ == "__main__":
    check_data_integrity()
