# Data Processing and Analysis
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine Learning
scikit-learn>=1.1.0
xgboost>=1.6.0
lightgbm>=3.3.0

# Deep Learning
tensorflow>=2.10.0
torch>=1.12.0
torchvision>=0.13.0

# Time Series
statsmodels>=0.13.0

# Optimization
gurobipy>=9.5.0
pulp>=2.6.0
ortools>=9.4.0

# Model Interpretability
shap>=0.41.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# Jupyter
jupyter>=1.0.0
ipykernel>=6.15.0

# Utilities
tqdm>=4.64.0
joblib>=1.1.0