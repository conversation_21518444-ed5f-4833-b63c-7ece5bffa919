# 航空公司机型分配优化系统 - 产品需求文档 (PRD)

## 项目概述

本项目为A航空公司并购B公司后制定科学的机型分配优化方案，目标是为单日815个航班分配最优机型，最大化总利润或最小化总成本。项目融合机器学习、强化学习和传统运筹学方法，构建一个具备预测能力和动态适应性的智能优化系统。

### 项目背景
- 并购后航班数量：815次/日（A公司480次 + B公司335次）
- 机队规模：211架飞机，覆盖9种机型
- 产品组合：47,190种不同的OD对-航班组合-票价等级产品
- 核心挑战：旅客需求不稳定性、飞机座位易逝性、机队运力过剩问题

## 核心功能需求

### 1. 数据处理与预处理模块
**目标**：建立统一、准确的数据基础，支持后续分析和优化

**功能需求**：
- **航班数据处理**：解析data_fam_schedule.csv，处理815个航班的时刻表信息
  - UTC时间转换：将当地时间+时差转换为统一UTC时间基准
  - 飞行时长计算：精确计算每个航班的实际飞行时间
  - 经停航班识别：识别多航段经停航班（如AA0055），确保作为整体分配
  - 飞机周转时间计算：考虑40分钟最小停留时间约束

- **机队信息管理**：处理data_fam_fleet.csv中的9种机型信息
  - 机型属性：座位数（48-165座）、数量、每小时飞行成本
  - 机型兼容性分析：不同机型对不同航线的适配性
  - 机队约束管理：确保分配不超过可用飞机数量

- **产品销售数据处理**：处理data_fam_products.csv的47,190种产品
  - RDx累积销售数据转换：转换为增量销售量时间序列
  - 产品特征提取：OD对、航班组合、票价等级、价格信息
  - 预订曲线构建：基于RD0-RD330的历史销售模式

- **市场份额数据集成**：整合data_fam_market_share.csv的竞争环境信息

### 2. 机器学习需求预测模块
**目标**：准确预测47,190种产品的旅客需求，为优化提供数据驱动输入

**核心算法需求**：
- **特征工程**：
  - 时间特征：距离起飞天数、星期几、月份、节假日指示器
  - 产品属性：起止机场、航班段数、票价等级、价格水平
  - 市场环境：A公司市场份额、竞争强度
  - 衍生特征：飞行时长、中转次数、转机时间

- **模型选择与实现**：
  - **树模型**：XGBoost用于特征重要性分析和基准预测
  - **深度学习**：LSTM/GRU处理时序数据，TFT融合静态和时变特征
  - **统计基线**：ARIMA、Holt-Winters作为对比基准
  - **模型评估**：MAE、RMSE、R²等指标，时间序列交叉验证

- **特征重要性分析**：
  - SHAP值分析影响销售的关键因素
  - 价格弹性分析：不同票价等级的需求敏感度
  - 预订行为模式：商务vs休闲旅客的时间偏好

### 3. 混合整数线性规划优化引擎
**目标**：基于预测需求，求解最优机型分配方案

**数学模型需求**：
- **目标函数**：最大化单日总利润 = 总收益 - 总运营成本
- **决策变量**：
  - X_ft：航班f分配给机型t的二元变量
  - Y_at：机场a的机型t飞机数量
  - Z_f1_f2_t：机型t飞机连续执飞f1、f2的连接变量

- **约束条件实现**：
  - 航班覆盖：每航班必须且仅能分配一种机型
  - 飞机平衡：机场间飞机流量平衡
  - 机队规模：不超过可用飞机数量
  - 最小停留时间：40分钟周转时间约束
  - 经停连续性：经停航班各段使用同一机型
  - 容量约束：需求不超过分配机型座位数

- **求解器需求**：
  - 商业求解器：Gurobi/CPLEX处理大规模问题
  - 启发式算法：应对超大规模或实时需求
  - 分解技术：列生成等高级优化方法

### 4. 强化学习动态优化框架（概念实现）
**目标**：为未来动态运营和实时调整建立框架

**系统设计需求**：
- **状态空间定义**：
  - 实时飞机位置和状态
  - 航班延误和中断信息
  - 动态需求更新
  - 剩余可用机队资源

- **动作空间设计**：
  - 机型重新分配
  - 飞机重新部署
  - 应急调度决策
  - 动态价格调整（扩展功能）

- **奖励函数设计**：
  - 利润最大化奖励
  - 延误惩罚机制
  - 运营中断成本
  - 客户满意度指标

### 5. 收益与成本分析模块
**目标**：全面评估并购前后和优化方案的财务表现

**分析需求**：
- **并购前基线建立**：
  - A公司独立运营估算（480航班）
  - 基于历史数据的收益/成本重构
  - 朴素分配方案的基准对比

- **优化方案收益计算**：
  - 产品级收益：预测需求×票价×容量约束
  - 航班级收益聚合：考虑机型容量限制
  - 总收益：所有航班收益求和

- **运营成本计算**：
  - 直接飞行成本：飞行时长×机型时薪
  - 地面运营成本：停机费、地勤费用（概念性）
  - 机会成本：闲置时间的收益损失

- **敏感性分析**：
  - 需求波动±5%/10%的影响
  - 燃油价格变化的成本影响
  - 市场份额变化的收益影响

### 6. 机队利用率分析模块
**目标**：评估各机型使用效率，识别优化空间

**分析指标需求**：
- **利用率指标**：
  - 每日飞行小时数vs理论最大值
  - 载客率：实际乘客/可用座位
  - 资产周转效率：收益/资产价值
  - 周转时间分析：实际vs最小停留时间

- **机型对比分析**：
  - 各机型盈利能力排序
  - 高/低利用率机型识别
  - 运力过剩/不足分析
  - ROI和资本效率对比

### 7. 可视化与报告系统
**目标**：提供直观的分析结果和决策支持

**可视化需求**：
- **仪表板**：关键KPI实时监控
- **网络图**：航班网络和机型分配可视化
- **时间序列图**：需求预测和历史对比
- **热力图**：机场/时段的运力分布
- **成本收益分析图**：各维度的财务表现

**报告生成需求**：
- **学术报告**：符合课程设计要求的完整分析报告
- **管理报告**：面向决策层的执行摘要
- **技术文档**：模型参数、算法说明、验证结果
- **演示材料**：PPT格式的项目展示

## 技术架构要求

### 开发环境与工具栈
- **编程语言**：Python 3.8+ （数据科学和优化）
- **机器学习**：Scikit-learn, XGBoost, TensorFlow/PyTorch
- **优化求解**：Gurobi, CPLEX, OR-Tools
- **数据处理**：Pandas, NumPy, Dask（大数据处理）
- **可视化**：Matplotlib, Plotly, Dash/Streamlit
- **数据库**：PostgreSQL/SQLite（数据存储）

### 系统架构设计
- **模块化设计**：独立的数据、模型、优化、分析模块
- **配置管理**：YAML/JSON配置文件管理参数
- **日志系统**：完整的运行日志和错误跟踪
- **测试框架**：单元测试、集成测试、性能测试
- **文档系统**：API文档、用户手册、技术说明

## 项目交付物

### 核心交付物
1. **完整源代码**：包含所有模块的可运行代码
2. **优化结果**：815个航班的最优机型分配方案
3. **分析报告**：并购前后对比、收益成本分析、机队建议
4. **学术材料**：课程设计报告、展示PPT、技术文档

### 扩展交付物
5. **交互式系统**：基于Web的分析和可视化平台
6. **API接口**：供外部系统调用的标准接口
7. **配置工具**：参数调整和场景分析工具
8. **部署指南**：系统安装、配置、运行说明

## 质量指标与验证

### 预测准确性
- 需求预测MAPE < 15%
- 收益预测误差 < 10%
- 模型解释性R² > 0.8

### 优化性能
- 815航班优化求解时间 < 30分钟
- 最优解gap < 5%
- 约束满足率 100%

### 系统性能
- 数据处理速度 > 1000记录/秒
- 可视化响应时间 < 3秒
- 报告生成时间 < 5分钟

### 业务价值
- 利润提升 > 5%（相比朴素方案）
- 机队利用率提升 > 10%
- 载客率提升 > 8%

## 风险管理与应对

### 技术风险
- **数据质量问题**：建立数据验证和清洗管道
- **算法复杂性**：从简单模型开始，逐步优化
- **计算性能**：使用高效算法和并行计算
- **集成复杂性**：采用模块化设计，接口标准化

### 项目风险  
- **时间约束**：优先核心功能，分阶段交付
- **资源限制**：合理分配开发重点，必要时简化需求
- **需求变更**：建立版本控制和变更管理流程

### 数据风险
- **数据缺失**：建立数据插补和估算方法
- **数据偏差**：多模型验证和交叉校验
- **隐私安全**：数据脱敏和访问控制

## 成功标准

### 技术成功标准
1. 系统能够成功处理所有4个数据文件
2. 机器学习模型达到预设准确性指标
3. 优化引擎能够求解815航班分配问题
4. 所有约束条件得到满足
5. 系统运行稳定，无严重错误

### 业务成功标准
1. 明确量化并购对A公司的财务影响
2. 识别出影响机票销售的关键因素
3. 提供可执行的机型分配方案
4. 给出具体的机队调整建议
5. 生成符合学术要求的完整报告

### 用户体验标准
1. 系统界面直观易用
2. 分析结果清晰易懂
3. 报告内容完整专业
4. 技术文档详实可用
5. 系统响应及时稳定

这个增强版PRD为A航空公司机型分配优化项目提供了全面的需求规范，融合了先进的机器学习、运筹优化和数据分析技术，确保项目能够交付具有实际业务价值的优化方案。