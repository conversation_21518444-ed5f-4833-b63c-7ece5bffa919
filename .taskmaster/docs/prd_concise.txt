# 航空公司机型分配优化系统 - PRD

## 项目概述
A航空公司并购B公司后，需要为815个每日航班和211架飞机(9种机型)制定优化的机型分配方案，最大化收益并降低成本。

## 核心任务

### 1. 数据预处理系统
处理4个CSV数据文件：航班时刻表、机队信息、产品销售历史、市场份额数据。
实现时区转换、经停航班识别、数据清洗和特征工程。

### 2. 需求预测模型
基于历史销售数据构建机器学习模型(XGBoost/LSTM)预测旅客需求。
分析RDx时间序列数据，建立预订曲线模型。

### 3. 并购前后收益分析
建立并购前基线(A公司独立运营)和并购后基线(朴素分配)。
对比分析财务表现和运营效率变化。

### 4. 机型分配优化模型
构建混合整数线性规划(MILP)模型，目标函数为最大化单日总利润。
实现关键约束：航班覆盖、机队平衡、40分钟停留时间、经停航班连续性。

### 5. 机队利用率分析
计算各机型的飞机利用率、载客率、周转效率。
分析每种机型的收益贡献和成本效益。

### 6. 敏感性分析
测试需求波动(±5%,±10%)和成本变化对方案的影响。
评估解决方案的鲁棒性。

### 7. 特征重要性分析
使用SHAP分析影响机票销售的关键因素。
识别旅客行程选择行为模式。

### 8. 报告生成系统
生成完整的课程设计报告包含：摘要、问题描述、研究过程、结果分析、结论。
创建PPT演示文稿和可视化图表。

## 技术要求
- Python 3.8+, Pandas, NumPy, Scikit-learn, XGBoost
- TensorFlow/PyTorch for深度学习
- Gurobi/PuLP for优化求解
- Matplotlib/Plotly for可视化

## 验收标准
- 需求预测模型R² > 0.8
- 成功求解MILP模型为所有815个航班分配机型
- 并购后利润提升 > 15%
- 完整报告和PPT交付

## 项目结构
```
src/
├── data_preprocessing/    # 数据预处理
├── demand_prediction/     # 需求预测
├── optimization/          # MILP优化模型
├── analysis/             # 财务和机队分析
└── reporting/            # 报告生成
```

这个项目将为A航空公司提供科学的机型分配策略，显著提升运营效率和盈利能力。