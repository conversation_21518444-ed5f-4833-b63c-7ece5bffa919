# 航空公司机型分配优化系统 - 产品需求文档 (PRD)

## 项目概述

### 背景
A航空公司近期完成了对B公司的并购，每日航班数从480次增长到815次，机队规模扩大到211架飞机，包含9种不同机型。需要为并购后的新运营环境制定优化的机型分配方案，以最大化收益并降低运营成本。

### 项目目标
1. 分析并购前后的收益情况对比
2. 分析旅客行程选择行为和影响销售的因素
3. 制定新的机型分配方案，为每日815个航班指派最适合的机型
4. 分析机队使用情况并提出优化建议
5. 提供完整的课程设计报告和PPT展示

### 核心挑战
- 旅客需求的不稳定性和座位易逝性
- 机队运力过剩问题
- 经停航班的连续性要求
- 40分钟最小停留时间约束
- 复杂的航班网络优化

## 技术要求

### 数据处理与分析
1. **数据预处理模块**
   - 航班时刻表数据处理 (data_fam_schedule.csv)
   - 机队信息数据整理 (data_fam_fleet.csv)
   - 产品销售历史数据分析 (data_fam_products.csv)
   - 市场份额数据处理 (data_fam_market_share.csv)
   - 时区转换和UTC时间标准化
   - 经停航班识别与处理

2. **需求预测系统**
   - 机器学习模型构建 (XGBoost, LSTM, GRU)
   - RDx数据的时间序列分析
   - 预订曲线建模
   - 特征工程 (时间特征、产品属性、市场环境)
   - 模型验证与性能评估

3. **收益基线分析**
   - 并购前A公司独立运营收益估算
   - 并购后朴素分配方案基线
   - 财务影响分析和效率提升评估

### 优化模型开发

4. **混合整数线性规划 (MILP) 模型**
   - 目标函数：最大化单日总利润
   - 决策变量定义 (X_ft, Y_at, Z_f1_f2_t)
   - 约束条件实现：
     * 航班覆盖约束
     * 飞机平衡约束
     * 机队规模约束
     * 最小停留时间约束 (40分钟)
     * 经停航班连续性约束
     * 容量约束

5. **强化学习框架 (概念性)**
   - 动态环境状态定义
   - 行动空间设计
   - 奖励函数构建
   - 实时调整能力

6. **求解器实现**
   - 商业级MILP求解器集成 (Gurobi/CPLEX)
   - 元启发式算法备选方案
   - 性能优化和可扩展性

### 分析与报告系统

7. **机队利用率分析**
   - 飞机利用率计算 (每日飞行小时数)
   - 载客率分析
   - 周转效率评估
   - 每种机型详细性能分析

8. **敏感性分析**
   - 需求波动影响分析 (+/-5%, +/-10%)
   - 成本波动敏感性测试
   - 鲁棒性评估

9. **特征重要性分析**
   - SHAP值分析
   - 影响销售的关键因素识别
   - 旅客行为模式洞察

### 输出要求

10. **报告生成系统**
    - 自动化报告生成
    - 可视化图表创建
    - 关键指标仪表板
    - PPT演示文稿模板

11. **结果分析模块**
    - 并购前后收益对比
    - 新方案财务表现评估
    - 机队使用情况详细分解
    - 优化建议生成

## 技术栈

### 编程语言与框架
- Python 3.8+ (主要开发语言)
- Pandas, NumPy (数据处理)
- Scikit-learn, XGBoost (机器学习)
- TensorFlow/PyTorch (深度学习)
- Gurobi/PuLP (优化求解)

### 数据分析工具
- Jupyter Notebook (交互式分析)
- Matplotlib, Seaborn, Plotly (可视化)
- SHAP (模型解释)

### 优化工具
- Gurobi Optimizer (MILP求解)
- OR-Tools (Google优化工具)
- NetworkX (网络分析)

## 项目结构

```
flight-allocation-optimization/
├── data/                          # 原始数据文件
│   ├── data_fam_schedule.csv
│   ├── data_fam_fleet.csv
│   ├── data_fam_products.csv
│   └── data_fam_market_share.csv
├── src/                           # 源代码
│   ├── data_preprocessing/        # 数据预处理模块
│   ├── demand_prediction/         # 需求预测模型
│   ├── optimization/              # 优化模型
│   ├── analysis/                  # 分析模块
│   └── utils/                     # 工具函数
├── models/                        # 训练好的模型
├── results/                       # 结果输出
├── notebooks/                     # Jupyter分析笔记本
├── docs/                          # 文档
├── tests/                         # 测试代码
└── requirements.txt               # 依赖包列表
```

## 验收标准

### 功能要求
1. 成功处理所有4个CSV数据文件
2. 构建准确的需求预测模型 (R² > 0.8)
3. 实现可行的MILP机型分配模型
4. 生成完整的财务对比分析
5. 提供详细的机队利用率报告

### 性能要求
1. 数据处理时间 < 5分钟
2. 模型训练时间 < 30分钟
3. 优化求解时间 < 60分钟
4. 内存使用 < 8GB

### 输出要求
1. 完整的课程设计报告 (包含摘要、问题描述、研究过程、结果分析、结论)
2. PPT演示文稿
3. 可复现的代码和结果
4. 详细的技术文档

## 时间计划

### 第一阶段：数据预处理与探索性分析 (3-4天)
- 数据清洗和格式化
- 时区转换和时间计算
- 经停航班识别
- 基础统计分析

### 第二阶段：需求预测模型开发 (4-5天)
- 特征工程
- 模型选择和训练
- 模型验证和调优
- 特征重要性分析

### 第三阶段：优化模型构建 (5-6天)
- MILP模型设计
- 约束条件实现
- 求解器集成和调优
- 结果验证

### 第四阶段：分析与报告 (3-4天)
- 财务对比分析
- 机队利用率分析
- 敏感性分析
- 报告撰写和PPT制作

## 风险评估

### 技术风险
- 大规模优化问题求解复杂度
- 机器学习模型准确性
- 数据质量和完整性

### 缓解措施
- 采用分解算法和启发式方法
- 多模型集成和交叉验证
- 数据清洗和异常值处理

## 成功指标

1. **财务指标**
   - 并购后利润提升 > 15%
   - 运营成本降低 > 10%
   - 机队利用率提升 > 20%

2. **运营指标**
   - 载客率提升 > 10%
   - 航班覆盖率 = 100%
   - 约束满足率 = 100%

3. **学术指标**
   - 报告质量评分 > 85分
   - 技术方案创新性
   - 结果可重现性

这个项目将为A航空公司提供一个科学、高效且具备未来适应性的机型分配策略，显著提升其经济效益和市场竞争力。