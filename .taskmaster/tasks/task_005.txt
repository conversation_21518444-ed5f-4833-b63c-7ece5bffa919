# Task ID: 5
# Title: Fleet Utilization and Contribution Analysis
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Analyze the results from the optimization model to calculate key performance indicators for the aircraft fleet. This includes utilization rates, load factors, and the overall profit contribution of each aircraft model.
# Details:
Using the solved MILP assignment from Task 3, calculate the following metrics for each of the 9 aircraft types: 1. Aircraft Utilization Rate (total flight hours per day). 2. Average Passenger Load Factor (percentage of seats filled). 3. Turnaround Efficiency (time on ground vs. minimum required). 4. Profit Contribution per Model. Use Pandas for data aggregation and Matplotlib/Plotly to create visualizations for these metrics.

# Test Strategy:
Verify the correctness of the metric calculations by cross-referencing with the raw optimization output. Ensure the analysis covers all 9 aircraft types. The metrics should be plausible and provide clear insights into the operational performance of each model.
