{"master": {"tasks": [{"id": 1, "title": "Data Ingestion and Preprocessing System", "description": "Develop a system to ingest and preprocess data from four distinct CSV files: flight schedules, fleet information, historical sales, and market share data. This includes handling time zone conversions, identifying multi-segment flights, cleaning data, and performing feature engineering.", "details": "Use the Pandas library to load the four CSV files into DataFrames. Implement a function to convert all time-related columns to a consistent UTC timezone using `pandas.to_datetime` and `tz_convert`. Write logic to identify and link segments of multi-stop flights based on aircraft and time continuity. Perform data cleansing by handling missing values (NaN) and removing duplicates. Engineer new features such as flight duration, day of the week, and booking period from existing data to be used in subsequent models.", "testStrategy": "Validate data loading by checking DataFrame shapes and data types. Write unit tests to confirm the accuracy of time zone conversions. Verify data integrity by checking for null values and duplicates after cleaning. Assert that engineered features have been correctly appended and have realistic values.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Load and validate CSV data files", "description": "Load the four CSV files (schedule, fleet, products, market_share) and perform initial data validation", "details": "Use pandas to load data_fam_schedule.csv, data_fam_fleet.csv, data_fam_products.csv, and data_fam_market_share.csv. Check data types, missing values, and basic statistics.", "status": "done", "dependencies": [], "parentTaskId": 1}, {"id": 2, "title": "Implement time zone conversion and UTC standardization", "description": "Convert all flight times to UTC standard using depoff and arroff timezone information", "details": "Process deptime/arrtime with depoff/arroff to convert local times to UTC. Calculate accurate flight durations and turnaround times between flights.", "status": "done", "dependencies": [], "parentTaskId": 1}, {"id": 3, "title": "Identify and process multi-segment flights", "description": "Detect multi-segment (stop-over) flights that must use the same aircraft type", "details": "Analyze flight data to identify multi-segment flights (like AA0055 in the example). Group related flight segments that must be assigned to the same aircraft type for operational continuity.", "status": "done", "dependencies": [], "parentTaskId": 1}, {"id": 4, "title": "Feature engineering for demand prediction", "description": "Extract and create features from RDx sales data and integrate with other datasets", "details": "Convert RDx cumulative sales to incremental sales per booking period. Create time-based features (days to departure, day of week, month). Extract product attributes and market share features. Merge all datasets on common keys.", "status": "done", "dependencies": [], "parentTaskId": 1}]}, {"id": 2, "title": "Demand Forecasting Model with XGBoost/LSTM", "description": "Build, train, and validate a machine learning model to forecast passenger demand. This model will be based on historical sales data and will analyze RDx time-series to create booking curve models.", "details": "Using the preprocessed data from Task 1, implement a demand forecasting model. The primary choice is XGBoost using the `xgboost` library due to its performance. Alternatively, an LSTM model can be built using TensorFlow or PyTorch for time-series analysis. The model should be trained on historical sales data (RDx) to predict demand for each flight. The implementation should be structured within the `src/demand_prediction/` directory.", "testStrategy": "Partition the data into training and testing sets (e.g., 80/20 split). Train the model and evaluate its performance using the R-squared metric, targeting a score greater than 0.8 as per the acceptance criteria. Plot predicted vs. actual demand to visually inspect model accuracy.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Prepare training and validation datasets", "description": "Split the processed data into training and validation sets for model development", "details": "Create time-series aware train/validation split. Ensure no data leakage between sets. Normalize features and handle categorical variables. Create baseline demand predictions for comparison.", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 2, "title": "Implement and train XGBoost model", "description": "Build XGBoost regression model for passenger demand prediction", "details": "Implement XGBoost model with appropriate hyperparameters. Train on historical RDx sales data. Perform cross-validation and hyperparameter tuning. Target R² > 0.8 performance.", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 3, "title": "Implement LSTM/GRU time series model", "description": "Build deep learning model for time series demand forecasting", "details": "Implement LSTM or GRU neural network for sequential RDx data. Design appropriate network architecture with attention to time dependencies. Compare performance with XGBoost model.", "status": "done", "dependencies": [], "parentTaskId": 2}]}, {"id": 3, "title": "Fleet Assignment Optimization with MILP", "description": "Construct a Mixed Integer Linear Programming (MILP) model to solve the fleet assignment problem. The model objective is to maximize the total daily profit by optimally assigning 211 aircraft of 9 types to 815 daily flights.", "details": "Use the Gurobi or PuLP optimization library to formulate the MILP model. Define the decision variables, which represent the assignment of an aircraft type to a flight leg. The objective function will be to maximize total profit, calculated as (revenue from forecasted demand) - (flight operating costs). Key constraints to implement are: 1. Flight Coverage: Every flight must be assigned exactly one aircraft. 2. Fleet Balance: The number of aircraft of a specific type arriving at an airport must equal the number departing. 3. Turnaround Time: Ensure a minimum of 40 minutes of ground time for an aircraft between flights. 4. Continuity for multi-segment flights.", "testStrategy": "Test the model on a smaller subset of flights and aircraft to ensure it is solvable and logically sound. Verify that the final solution for all 815 flights satisfies all defined constraints. The primary success criterion is the successful generation of a complete and valid assignment for all flights.", "priority": "high", "dependencies": [1, 2], "status": "done", "subtasks": [{"id": 1, "title": "Define MILP decision variables and objective function", "description": "Formulate the mathematical model structure for fleet assignment optimization", "details": "Define decision variables X_ft (flight-aircraft assignment), Y_at (airport aircraft balance), Z_f1_f2_t (aircraft rotations). Formulate objective function to maximize total daily profit (revenue - costs).", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 2, "title": "Implement flight coverage and fleet balance constraints", "description": "Add constraints ensuring each flight is assigned exactly one aircraft type and fleet balance", "details": "Implement constraint that each flight must be assigned to exactly one aircraft type. Add fleet balance constraints ensuring aircraft arrivals equal departures at each airport for each aircraft type.", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 3, "title": "Implement turnaround time and continuity constraints", "description": "Add constraints for 40-minute minimum turnaround and multi-segment flight continuity", "details": "Implement 40-minute minimum turnaround time constraint between consecutive flights. Add constraints ensuring multi-segment flights use the same aircraft type throughout all segments.", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 4, "title": "Implement capacity and fleet size constraints", "description": "Add constraints for passenger capacity limits and available aircraft quantities", "details": "Implement constraints ensuring passenger demand doesn't exceed aircraft seat capacity. Add fleet size constraints limiting aircraft usage to available quantities for each aircraft type (211 total aircraft, 9 types).", "status": "done", "dependencies": [], "parentTaskId": 3}, {"id": 5, "title": "Solve MILP model and extract optimal assignment", "description": "Use Gurobi/CPLEX solver to find optimal solution and extract aircraft assignments", "details": "Configure and run commercial MILP solver (Gurobi or CPLEX) to solve the fleet assignment problem. Extract optimal aircraft type assignments for all 815 flights. Validate solution feasibility and optimality.", "status": "done", "dependencies": [], "parentTaskId": 3}]}, {"id": 4, "title": "Pre/Post-Merger Financial and Operational Analysis", "description": "Establish financial baselines for pre-merger and a naive post-merger scenario. Compare these baselines against the optimized solution to quantify the financial uplift and operational efficiency gains.", "details": "Calculate the total profit for two baseline scenarios: 1. Pre-merger baseline: A Airlines operating independently. 2. Post-merger naive baseline: A simple, non-optimized assignment of aircraft post-merger. Use Pandas to perform the financial calculations. Compare the profit from these scenarios with the profit generated by the MILP optimization model (from Task 3). The goal is to demonstrate a profit increase of over 15%.", "testStrategy": "Unit test the profit calculation logic for both baseline scenarios. Validate the final comparison to ensure the percentage uplift is calculated correctly. The result must meet the >15% profit improvement acceptance criterion.", "priority": "medium", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 5, "title": "Fleet Utilization and Contribution Analysis", "description": "Analyze the results from the optimization model to calculate key performance indicators for the aircraft fleet. This includes utilization rates, load factors, and the overall profit contribution of each aircraft model.", "details": "Using the solved MILP assignment from Task 3, calculate the following metrics for each of the 9 aircraft types: 1. Aircraft Utilization Rate (total flight hours per day). 2. Average Passenger Load Factor (percentage of seats filled). 3. Turnaround Efficiency (time on ground vs. minimum required). 4. Profit Contribution per Model. Use Pandas for data aggregation and Matplotlib/Plotly to create visualizations for these metrics.", "testStrategy": "Verify the correctness of the metric calculations by cross-referencing with the raw optimization output. Ensure the analysis covers all 9 aircraft types. The metrics should be plausible and provide clear insights into the operational performance of each model.", "priority": "medium", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 6, "title": "Sensitivity Analysis on Demand and Cost", "description": "Evaluate the robustness of the fleet assignment solution by testing its performance under various demand and cost scenarios. This will help understand how market fluctuations might impact profitability.", "details": "Re-run the MILP optimization model (from Task 3) under several modified conditions. Create scenarios where the forecasted demand (from Task 2) is adjusted by +5%, -5%, +10%, and -10%. Similarly, create scenarios where operating costs are adjusted by the same percentages. Analyze and document the changes in the optimal assignment and total profit for each scenario.", "testStrategy": "For each scenario, confirm that the model produces a new, valid solution. Document the resulting profit and compare it against the original optimized solution. The analysis should demonstrate the stability and robustness of the proposed assignment strategy.", "priority": "medium", "dependencies": [3], "status": "done", "subtasks": []}, {"id": 7, "title": "Feature Importance Analysis using SHAP", "description": "Use the SHAP (SHapley Additive exPlanations) library to analyze the trained demand prediction model. The goal is to identify the key factors that influence ticket sales and understand passenger behavior.", "details": "Apply the SHAP library to the trained XGBoost model from Task 2. Generate SHAP value plots (e.g., summary plot, dependence plots) to visualize the impact of each feature on the demand prediction. This analysis will help identify which factors, such as time of day, route, or booking window, are most critical in driving sales.", "testStrategy": "Generate and review SHAP summary plots to ensure they are interpreted correctly. The analysis should yield a clear, ranked list of the most influential features. Document these findings as part of the model explanation.", "priority": "medium", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 8, "title": "Automated Report and Presentation Generation", "description": "Develop a system to automatically generate a comprehensive project report and a summary presentation. The output should include an abstract, problem description, methodology, results, and conclusions, along with supporting visualizations.", "details": "Consolidate all analyses and results from the preceding tasks. Use libraries like Matplotlib and Plotly to generate all necessary charts and graphs (e.g., demand forecasts, profit comparisons, utilization charts). Structure the project findings into a complete report document. A library like `python-pptx` or a markup language tool can be used to generate the final PPT and report.", "testStrategy": "Review the generated report and presentation for completeness, accuracy, and clarity. Ensure all sections of the PRD are addressed and all key results from the project are included and correctly visualized. The final deliverable must be a complete, well-formatted report and an accompanying PPT.", "priority": "low", "dependencies": [4, 5, 6, 7], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-19T11:07:26.446Z", "updated": "2025-07-29T21:27:23.466Z", "description": "Tasks for master context"}}}