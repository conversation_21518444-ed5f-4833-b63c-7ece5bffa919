# Task ID: 2
# Title: Demand Forecasting Model with XGBoost/LSTM
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Build, train, and validate a machine learning model to forecast passenger demand. This model will be based on historical sales data and will analyze RDx time-series to create booking curve models.
# Details:
Using the preprocessed data from Task 1, implement a demand forecasting model. The primary choice is XGBoost using the `xgboost` library due to its performance. Alternatively, an LSTM model can be built using TensorFlow or PyTorch for time-series analysis. The model should be trained on historical sales data (RDx) to predict demand for each flight. The implementation should be structured within the `src/demand_prediction/` directory.

# Test Strategy:
Partition the data into training and testing sets (e.g., 80/20 split). Train the model and evaluate its performance using the R-squared metric, targeting a score greater than 0.8 as per the acceptance criteria. Plot predicted vs. actual demand to visually inspect model accuracy.

# Subtasks:
## 1. Prepare training and validation datasets [pending]
### Dependencies: None
### Description: Split the processed data into training and validation sets for model development
### Details:
Create time-series aware train/validation split. Ensure no data leakage between sets. Normalize features and handle categorical variables. Create baseline demand predictions for comparison.

## 2. Implement and train XGBoost model [pending]
### Dependencies: None
### Description: Build XGBoost regression model for passenger demand prediction
### Details:
Implement XGBoost model with appropriate hyperparameters. Train on historical RDx sales data. Perform cross-validation and hyperparameter tuning. Target R² > 0.8 performance.

## 3. Implement LSTM/GRU time series model [pending]
### Dependencies: None
### Description: Build deep learning model for time series demand forecasting
### Details:
Implement LSTM or GRU neural network for sequential RDx data. Design appropriate network architecture with attention to time dependencies. Compare performance with XGBoost model.

