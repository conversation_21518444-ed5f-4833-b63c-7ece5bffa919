# Task ID: 3
# Title: Fleet Assignment Optimization with MILP
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Construct a Mixed Integer Linear Programming (MILP) model to solve the fleet assignment problem. The model objective is to maximize the total daily profit by optimally assigning 211 aircraft of 9 types to 815 daily flights.
# Details:
Use the Gurobi or PuLP optimization library to formulate the MILP model. Define the decision variables, which represent the assignment of an aircraft type to a flight leg. The objective function will be to maximize total profit, calculated as (revenue from forecasted demand) - (flight operating costs). Key constraints to implement are: 1. Flight Coverage: Every flight must be assigned exactly one aircraft. 2. Fleet Balance: The number of aircraft of a specific type arriving at an airport must equal the number departing. 3. Turnaround Time: Ensure a minimum of 40 minutes of ground time for an aircraft between flights. 4. Continuity for multi-segment flights.

# Test Strategy:
Test the model on a smaller subset of flights and aircraft to ensure it is solvable and logically sound. Verify that the final solution for all 815 flights satisfies all defined constraints. The primary success criterion is the successful generation of a complete and valid assignment for all flights.

# Subtasks:
## 1. Define MILP decision variables and objective function [pending]
### Dependencies: None
### Description: Formulate the mathematical model structure for fleet assignment optimization
### Details:
Define decision variables X_ft (flight-aircraft assignment), Y_at (airport aircraft balance), Z_f1_f2_t (aircraft rotations). Formulate objective function to maximize total daily profit (revenue - costs).

## 2. Implement flight coverage and fleet balance constraints [pending]
### Dependencies: None
### Description: Add constraints ensuring each flight is assigned exactly one aircraft type and fleet balance
### Details:
Implement constraint that each flight must be assigned to exactly one aircraft type. Add fleet balance constraints ensuring aircraft arrivals equal departures at each airport for each aircraft type.

## 3. Implement turnaround time and continuity constraints [pending]
### Dependencies: None
### Description: Add constraints for 40-minute minimum turnaround and multi-segment flight continuity
### Details:
Implement 40-minute minimum turnaround time constraint between consecutive flights. Add constraints ensuring multi-segment flights use the same aircraft type throughout all segments.

## 4. Implement capacity and fleet size constraints [pending]
### Dependencies: None
### Description: Add constraints for passenger capacity limits and available aircraft quantities
### Details:
Implement constraints ensuring passenger demand doesn't exceed aircraft seat capacity. Add fleet size constraints limiting aircraft usage to available quantities for each aircraft type (211 total aircraft, 9 types).

## 5. Solve MILP model and extract optimal assignment [pending]
### Dependencies: None
### Description: Use Gurobi/CPLEX solver to find optimal solution and extract aircraft assignments
### Details:
Configure and run commercial MILP solver (Gurobi or CPLEX) to solve the fleet assignment problem. Extract optimal aircraft type assignments for all 815 flights. Validate solution feasibility and optimality.

