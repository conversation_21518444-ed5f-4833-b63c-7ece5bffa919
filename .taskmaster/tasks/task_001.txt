# Task ID: 1
# Title: Data Ingestion and Preprocessing System
# Status: pending
# Dependencies: None
# Priority: high
# Description: Develop a system to ingest and preprocess data from four distinct CSV files: flight schedules, fleet information, historical sales, and market share data. This includes handling time zone conversions, identifying multi-segment flights, cleaning data, and performing feature engineering.
# Details:
Use the Pandas library to load the four CSV files into DataFrames. Implement a function to convert all time-related columns to a consistent UTC timezone using `pandas.to_datetime` and `tz_convert`. Write logic to identify and link segments of multi-stop flights based on aircraft and time continuity. Perform data cleansing by handling missing values (NaN) and removing duplicates. Engineer new features such as flight duration, day of the week, and booking period from existing data to be used in subsequent models.

# Test Strategy:
Validate data loading by checking DataFrame shapes and data types. Write unit tests to confirm the accuracy of time zone conversions. Verify data integrity by checking for null values and duplicates after cleaning. Assert that engineered features have been correctly appended and have realistic values.

# Subtasks:
## 1. Load and validate CSV data files [pending]
### Dependencies: None
### Description: Load the four CSV files (schedule, fleet, products, market_share) and perform initial data validation
### Details:
Use pandas to load data_fam_schedule.csv, data_fam_fleet.csv, data_fam_products.csv, and data_fam_market_share.csv. Check data types, missing values, and basic statistics.

## 2. Implement time zone conversion and UTC standardization [pending]
### Dependencies: None
### Description: Convert all flight times to UTC standard using depoff and arroff timezone information
### Details:
Process deptime/arrtime with depoff/arroff to convert local times to UTC. Calculate accurate flight durations and turnaround times between flights.

## 3. Identify and process multi-segment flights [pending]
### Dependencies: None
### Description: Detect multi-segment (stop-over) flights that must use the same aircraft type
### Details:
Analyze flight data to identify multi-segment flights (like AA0055 in the example). Group related flight segments that must be assigned to the same aircraft type for operational continuity.

## 4. Feature engineering for demand prediction [pending]
### Dependencies: None
### Description: Extract and create features from RDx sales data and integrate with other datasets
### Details:
Convert RDx cumulative sales to incremental sales per booking period. Create time-based features (days to departure, day of week, month). Extract product attributes and market share features. Merge all datasets on common keys.

