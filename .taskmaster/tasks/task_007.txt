# Task ID: 7
# Title: Feature Importance Analysis using SHAP
# Status: pending
# Dependencies: 2
# Priority: medium
# Description: Use the SHAP (SHapley Additive exPlanations) library to analyze the trained demand prediction model. The goal is to identify the key factors that influence ticket sales and understand passenger behavior.
# Details:
Apply the SHAP library to the trained XGBoost model from Task 2. Generate SHAP value plots (e.g., summary plot, dependence plots) to visualize the impact of each feature on the demand prediction. This analysis will help identify which factors, such as time of day, route, or booking window, are most critical in driving sales.

# Test Strategy:
Generate and review SHAP summary plots to ensure they are interpreted correctly. The analysis should yield a clear, ranked list of the most influential features. Document these findings as part of the model explanation.
