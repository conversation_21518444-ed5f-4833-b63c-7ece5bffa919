# Task ID: 4
# Title: Pre/Post-Merger Financial and Operational Analysis
# Status: pending
# Dependencies: 3
# Priority: medium
# Description: Establish financial baselines for pre-merger and a naive post-merger scenario. Compare these baselines against the optimized solution to quantify the financial uplift and operational efficiency gains.
# Details:
Calculate the total profit for two baseline scenarios: 1. Pre-merger baseline: A Airlines operating independently. 2. Post-merger naive baseline: A simple, non-optimized assignment of aircraft post-merger. Use Pandas to perform the financial calculations. Compare the profit from these scenarios with the profit generated by the MILP optimization model (from Task 3). The goal is to demonstrate a profit increase of over 15%.

# Test Strategy:
Unit test the profit calculation logic for both baseline scenarios. Validate the final comparison to ensure the percentage uplift is calculated correctly. The result must meet the >15% profit improvement acceptance criterion.
