"""
完整工作流执行脚本
使用修订后的最佳预测模型执行完整工作流并生成报告
增强版本：包含完整的日志收集和监控功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json
import logging
import time
import traceback
import psutil
import platform

# JSON序列化辅助函数
def json_serializable(obj):
    """将对象转换为JSON可序列化的格式"""
    if isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(v) for v in obj]
    else:
        return obj

# 添加src到路径
sys.path.append('src')

# 配置日志系统
def setup_logging():
    """设置详细的日志系统"""
    log_dir = "results/logs"
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/workflow_execution_{timestamp}.log"

    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__), log_file

def collect_system_info():
    """收集系统信息"""
    return {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'cpu_count': psutil.cpu_count(),
        'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
        'disk_free_gb': round(psutil.disk_usage('.').free / (1024**3), 2),
        'timestamp': datetime.now().isoformat()
    }

def log_step_start(logger, step_name, step_number):
    """记录步骤开始"""
    logger.info(f"{'='*60}")
    logger.info(f"开始执行步骤 {step_number}: {step_name}")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{'='*60}")
    return time.time()

def log_step_end(logger, step_name, start_time, success=True, error_msg=None):
    """记录步骤结束"""
    duration = time.time() - start_time
    status = "成功" if success else "失败"

    logger.info(f"{'='*60}")
    logger.info(f"步骤 {step_name} 执行{status}")
    logger.info(f"执行时间: {duration:.2f} 秒")
    if not success and error_msg:
        logger.error(f"错误信息: {error_msg}")
    logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{'='*60}")

    return {
        'status': 'success' if success else 'failed',
        'duration_seconds': round(duration, 2),
        'start_time': datetime.fromtimestamp(start_time).isoformat(),
        'end_time': datetime.now().isoformat(),
        'error_message': error_msg if not success else None
    }

def log_memory_usage(logger, step_name):
    """记录内存使用情况"""
    memory = psutil.virtual_memory()
    logger.info(f"{step_name} - 内存使用: {memory.percent}% ({memory.used/(1024**3):.2f}GB/{memory.total/(1024**3):.2f}GB)")
    return {
        'memory_percent': memory.percent,
        'memory_used_gb': round(memory.used/(1024**3), 2),
        'memory_total_gb': round(memory.total/(1024**3), 2)
    }

def execute_complete_workflow():
    """执行完整的预测工作流 - 增强版本包含详细日志"""
    # 设置日志系统
    logger, log_file = setup_logging()

    print("=== 航空公司需求预测完整工作流执行 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"日志文件: {log_file}")
    print("=" * 50)

    # 记录系统信息
    system_info = collect_system_info()
    logger.info("系统信息收集完成")
    logger.info(f"系统平台: {system_info['platform']}")
    logger.info(f"Python版本: {system_info['python_version']}")
    logger.info(f"CPU核心数: {system_info['cpu_count']}")
    logger.info(f"总内存: {system_info['memory_total_gb']}GB")

    workflow_results = {
        'timestamp': datetime.now().isoformat(),
        'system_info': system_info,
        'log_file': log_file,
        'steps': {},
        'final_results': {},
        'performance_metrics': {}
    }
    
    try:
        # 步骤1: 数据预处理
        step_start_time = log_step_start(logger, "数据预处理", 1)
        print("\n📋 步骤1: 数据预处理")
        print("-" * 30)

        try:
            from src.data_preprocessing.preprocessing_pipeline import DataPreprocessingPipeline

            # 记录内存使用
            memory_before = log_memory_usage(logger, "数据预处理开始前")

            # 初始化预处理管道
            logger.info("初始化数据预处理管道")
            pipeline = DataPreprocessingPipeline(data_dir="data")

            # 检查输入文件
            input_files = ['data_fam_schedule.csv', 'data_fam_fleet.csv',
                          'data_fam_products.csv', 'data_fam_market_share.csv']
            file_info = {}
            for file in input_files:
                file_path = f"data/{file}"
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                    file_info[file] = {
                        'exists': True,
                        'size_mb': round(file_size, 2)
                    }
                    logger.info(f"输入文件 {file}: {file_size:.2f}MB")
                else:
                    file_info[file] = {'exists': False, 'size_mb': 0}
                    logger.warning(f"输入文件 {file} 不存在")

            # 运行预处理
            logger.info("开始执行数据预处理管道")
            preprocessing_start = time.time()
            processed_data = pipeline.run_pipeline()
            preprocessing_duration = time.time() - preprocessing_start

            # 保存处理后的数据
            logger.info("保存处理后的数据")
            save_start = time.time()
            pipeline.save_processed_data("results/processed_data")
            save_duration = time.time() - save_start

            # 检查输出文件
            output_files = ['features.csv', 'schedule.csv', 'products.csv',
                           'fleet.csv', 'market_share.csv']
            output_info = {}
            for file in output_files:
                file_path = f"results/processed_data/{file}"
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                    # 尝试读取行数
                    try:
                        df = pd.read_csv(file_path)
                        row_count = len(df)
                        col_count = len(df.columns)
                    except:
                        row_count = col_count = 0

                    output_info[file] = {
                        'exists': True,
                        'size_mb': round(file_size, 2),
                        'rows': row_count,
                        'columns': col_count
                    }
                    logger.info(f"输出文件 {file}: {file_size:.2f}MB, {row_count}行, {col_count}列")
                else:
                    output_info[file] = {'exists': False}
                    logger.error(f"输出文件 {file} 未生成")

            # 记录内存使用
            memory_after = log_memory_usage(logger, "数据预处理完成后")

            # 记录步骤结果
            step_result = log_step_end(logger, "数据预处理", step_start_time, True)

            workflow_results['steps']['data_preprocessing'] = {
                **step_result,
                'message': '数据预处理完成',
                'input_files': file_info,
                'output_files': output_info,
                'processing_duration': round(preprocessing_duration, 2),
                'save_duration': round(save_duration, 2),
                'memory_usage': {
                    'before': memory_before,
                    'after': memory_after
                }
            }

            print("✅ 数据预处理完成")
            print(f"   处理文件数: {len([f for f in output_info.values() if f.get('exists', False)])}")
            print(f"   处理时间: {preprocessing_duration:.2f}秒")

        except Exception as e:
            error_msg = f"数据预处理失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            step_result = log_step_end(logger, "数据预处理", step_start_time, False, error_msg)
            workflow_results['steps']['data_preprocessing'] = step_result
            raise
        
        # 步骤2: 需求预测
        step_start_time = log_step_start(logger, "需求预测", 2)
        print("\n📈 步骤2: 需求预测 (使用LSTM最佳模型)")
        print("-" * 30)

        try:
            from src.demand_prediction.data_preparation import prepare_dataset
            from src.demand_prediction.lstm_model import prepare_lstm_dataset, train_lstm_model

            # 记录内存使用
            memory_before = log_memory_usage(logger, "需求预测开始前")

            # 准备数据集
            logger.info("开始准备训练数据集")
            dataset_start = time.time()
            dataset = prepare_dataset("results/processed_data")
            dataset_duration = time.time() - dataset_start

            train_samples = len(dataset['X_train']) if 'X_train' in dataset else 0
            val_samples = len(dataset['X_val']) if 'X_val' in dataset else 0
            test_samples = len(dataset['X_test']) if 'X_test' in dataset else 0

            logger.info(f"数据集准备完成: 训练{train_samples}, 验证{val_samples}, 测试{test_samples}样本")
            print(f"   数据集准备完成: {train_samples} 训练样本")

            # 准备LSTM数据集
            logger.info("开始准备LSTM序列数据")
            lstm_prep_start = time.time()
            lstm_dataset = prepare_lstm_dataset(dataset, sequence_length=10)
            lstm_prep_duration = time.time() - lstm_prep_start

            lstm_train_sequences = len(lstm_dataset['X_train']) if 'X_train' in lstm_dataset else 0
            logger.info(f"LSTM序列准备完成: {lstm_train_sequences} 序列")
            print(f"   LSTM序列准备完成: {lstm_train_sequences} 序列")

            # 训练多个模型进行对比
            models_to_train = ['LSTM', 'GRU', 'XGBoost']
            model_results = {}

            for model_type in models_to_train:
                logger.info(f"开始训练 {model_type} 模型")
                model_start = time.time()

                try:
                    if model_type in ['LSTM', 'GRU']:
                        model, results = train_lstm_model(lstm_dataset, model_type=model_type)
                        model_file = f"models/{model_type.lower()}_model_enhanced.h5"
                        model.save_model(model_file)
                    else:  # XGBoost
                        from src.demand_prediction.xgboost_model import train_xgboost_model
                        model, results = train_xgboost_model(dataset)
                        model_file = f"models/xgboost_model_enhanced.joblib"
                        # 保存XGBoost模型的逻辑

                    model_duration = time.time() - model_start

                    # 记录模型训练日志
                    model_log = {
                        'model_type': model_type,
                        'training_duration': round(model_duration, 2),
                        'timestamp': datetime.now().isoformat(),
                        'metrics': results.get('test_metrics', {}),
                        'model_file': model_file,
                        'training_history': results.get('history', {})
                    }

                    # 保存详细的模型训练日志
                    log_dir = "results/model_logs"
                    os.makedirs(log_dir, exist_ok=True)
                    with open(f"{log_dir}/{model_type}_training_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w', encoding='utf-8') as f:
                        json.dump(model_log, f, ensure_ascii=False, indent=2)

                    model_results[model_type] = model_log

                    logger.info(f"{model_type} 模型训练完成")
                    if 'test_metrics' in results:
                        metrics = results['test_metrics']
                        logger.info(f"  R² Score: {metrics.get('r2_score', 0):.4f}")
                        logger.info(f"  RMSE: {metrics.get('rmse', 0):.2f}")
                        logger.info(f"  MAE: {metrics.get('mae', 0):.2f}")

                except Exception as e:
                    logger.error(f"{model_type} 模型训练失败: {str(e)}")
                    model_results[model_type] = {
                        'model_type': model_type,
                        'status': 'failed',
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }

            # 选择最佳模型
            best_model_type = 'LSTM'  # 默认选择
            best_metrics = None

            for model_type, result in model_results.items():
                if 'metrics' in result and 'r2_score' in result['metrics']:
                    if best_metrics is None or result['metrics']['r2_score'] > best_metrics.get('r2_score', 0):
                        best_model_type = model_type
                        best_metrics = result['metrics']

            logger.info(f"最佳模型: {best_model_type}")

            # 记录内存使用
            memory_after = log_memory_usage(logger, "需求预测完成后")

            # 记录步骤结果
            step_result = log_step_end(logger, "需求预测", step_start_time, True)

            workflow_results['steps']['demand_prediction'] = {
                **step_result,
                'model_used': f'{best_model_type} (最佳模型)',
                'best_metrics': best_metrics,
                'all_models': model_results,
                'dataset_preparation_duration': round(dataset_duration, 2),
                'lstm_preparation_duration': round(lstm_prep_duration, 2),
                'data_samples': {
                    'train': train_samples,
                    'validation': val_samples,
                    'test': test_samples,
                    'lstm_sequences': lstm_train_sequences
                },
                'memory_usage': {
                    'before': memory_before,
                    'after': memory_after
                }
            }

            print("✅ 需求预测完成")
            print(f"   最佳模型: {best_model_type}")
            if best_metrics:
                print(f"   R² Score: {best_metrics.get('r2_score', 0):.4f}")
                print(f"   RMSE: {best_metrics.get('rmse', 0):.2f}")
                print(f"   MAE: {best_metrics.get('mae', 0):.2f}")

        except Exception as e:
            error_msg = f"需求预测失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            step_result = log_step_end(logger, "需求预测", step_start_time, False, error_msg)
            workflow_results['steps']['demand_prediction'] = step_result
            raise
        
        # 步骤3: 舰队分配优化
        step_start_time = log_step_start(logger, "舰队分配优化", 3)
        print("\n✈️ 步骤3: 舰队分配优化")
        print("-" * 30)

        try:
            from src.optimization.fleet_optimizer import FleetOptimizationPipeline

            # 记录内存使用
            memory_before = log_memory_usage(logger, "优化开始前")

            # 初始化优化管道
            logger.info("初始化舰队分配优化管道")
            opt_pipeline = FleetOptimizationPipeline(data_dir="results/processed_data")

            # 运行优化
            logger.info("开始执行舰队分配优化")
            optimization_start = time.time()
            opt_results = opt_pipeline.run_pipeline()
            optimization_duration = time.time() - optimization_start

            # 保存结果
            logger.info("保存优化结果")
            save_start = time.time()
            opt_pipeline.save_results("results/optimization")
            save_duration = time.time() - save_start

            # 分析优化结果
            solution_quality = "unknown"
            if opt_results:
                if opt_results.get('status') == 'optimal':
                    solution_quality = "optimal"
                elif opt_results.get('status') == 'feasible':
                    solution_quality = "feasible"
                elif opt_results.get('status') == 'infeasible':
                    solution_quality = "infeasible"

            # 记录优化统计
            optimization_stats = {
                'solution_status': opt_results.get('status', 'unknown'),
                'objective_value': opt_results.get('objective_value', 0),
                'variables_count': opt_results.get('variables_count', 0),
                'constraints_count': opt_results.get('constraints_count', 0),
                'solve_time': opt_results.get('solve_time', 0),
                'gap': opt_results.get('gap', 0)
            }

            logger.info(f"优化完成 - 状态: {optimization_stats['solution_status']}")
            logger.info(f"目标函数值: {optimization_stats['objective_value']:,.0f}")
            logger.info(f"求解时间: {optimization_stats['solve_time']:.2f}秒")

            # 记录内存使用
            memory_after = log_memory_usage(logger, "优化完成后")

            # 记录步骤结果
            step_result = log_step_end(logger, "舰队分配优化", step_start_time, True)

            workflow_results['steps']['fleet_optimization'] = {
                **step_result,
                'message': '舰队分配优化完成',
                'optimization_stats': optimization_stats,
                'optimization_duration': round(optimization_duration, 2),
                'save_duration': round(save_duration, 2),
                'solution_quality': solution_quality,
                'memory_usage': {
                    'before': memory_before,
                    'after': memory_after
                }
            }

            print("✅ 舰队分配优化完成")
            print(f"   解决方案状态: {optimization_stats['solution_status']}")
            print(f"   目标函数值: {optimization_stats['objective_value']:,.0f}")
            print(f"   优化时间: {optimization_duration:.2f}秒")

        except Exception as e:
            error_msg = f"舰队分配优化失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            step_result = log_step_end(logger, "舰队分配优化", step_start_time, False, error_msg)
            workflow_results['steps']['fleet_optimization'] = step_result
            # 不抛出异常，继续执行后续步骤
            print("⚠️ 舰队分配优化失败，继续执行后续步骤")
        
        # 步骤4: 分析和评估
        step_start_time = log_step_start(logger, "分析和评估", 4)
        print("\n📊 步骤4: 分析和评估")
        print("-" * 30)

        try:
            from src.analysis.analysis_pipeline import AnalysisPipeline

            # 记录内存使用
            memory_before = log_memory_usage(logger, "分析开始前")

            # 初始化分析管道
            logger.info("初始化分析评估管道")
            analysis_pipeline = AnalysisPipeline(data_dir="results/processed_data")

            # 运行分析
            logger.info("开始执行分析评估")
            analysis_start = time.time()
            analysis_results = analysis_pipeline.run_pipeline()
            analysis_duration = time.time() - analysis_start

            # 记录分析结果统计
            analysis_stats = {
                'total_metrics': len(analysis_results) if analysis_results else 0,
                'analysis_types': list(analysis_results.keys()) if analysis_results else [],
                'duration': round(analysis_duration, 2)
            }

            logger.info(f"分析完成 - 生成指标数: {analysis_stats['total_metrics']}")

            # 记录内存使用
            memory_after = log_memory_usage(logger, "分析完成后")

            # 记录步骤结果
            step_result = log_step_end(logger, "分析和评估", step_start_time, True)

            workflow_results['steps']['analysis'] = {
                **step_result,
                'message': '分析评估完成',
                'analysis_stats': analysis_stats,
                'key_metrics': analysis_results,
                'memory_usage': {
                    'before': memory_before,
                    'after': memory_after
                }
            }

            print("✅ 分析评估完成")
            print(f"   分析时间: {analysis_duration:.2f}秒")
            if analysis_results:
                for key, value in list(analysis_results.items())[:3]:  # 显示前3个关键指标
                    print(f"   {key}: {value}")

        except Exception as e:
            error_msg = f"分析评估失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            step_result = log_step_end(logger, "分析和评估", step_start_time, False, error_msg)
            workflow_results['steps']['analysis'] = step_result
            print("⚠️ 分析评估失败，继续执行后续步骤")

        # 步骤5: 生成报告
        step_start_time = log_step_start(logger, "生成报告", 5)
        print("\n📄 步骤5: 生成报告")
        print("-" * 30)

        try:
            from src.reporting.report_generator import ReportGenerator

            # 记录内存使用
            memory_before = log_memory_usage(logger, "报告生成开始前")

            # 初始化报告生成器
            logger.info("初始化报告生成器")
            report_generator = ReportGenerator(results_dir="results")

            # 生成完整报告
            logger.info("生成Markdown报告")
            report_start = time.time()
            markdown_report = report_generator.generate_complete_report()
            report_duration = time.time() - report_start

            # 生成PPT演示文稿
            logger.info("生成PPT演示文稿")
            ppt_start = time.time()
            presentation = report_generator.create_presentation()
            ppt_duration = time.time() - ppt_start

            # 检查生成的文件
            generated_files = []
            report_files = [
                'results/reports/fleet_assignment_report.md',
                'results/reports/fleet_assignment_presentation.pptx'
            ]

            for file_path in report_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    generated_files.append({
                        'file': os.path.basename(file_path),
                        'path': file_path,
                        'size_kb': round(file_size, 2),
                        'exists': True
                    })
                    logger.info(f"生成文件: {file_path} ({file_size:.2f}KB)")
                else:
                    generated_files.append({
                        'file': os.path.basename(file_path),
                        'path': file_path,
                        'exists': False
                    })
                    logger.warning(f"文件未生成: {file_path}")

            # 记录内存使用
            memory_after = log_memory_usage(logger, "报告生成完成后")

            # 记录步骤结果
            step_result = log_step_end(logger, "生成报告", step_start_time, True)

            workflow_results['steps']['reporting'] = {
                **step_result,
                'message': '报告生成完成',
                'generated_files': generated_files,
                'report_generation_duration': round(report_duration, 2),
                'ppt_generation_duration': round(ppt_duration, 2),
                'memory_usage': {
                    'before': memory_before,
                    'after': memory_after
                }
            }

            print("✅ 报告生成完成")
            print(f"   生成报告数: {len([f for f in generated_files if f['exists']])}")
            print(f"   报告生成时间: {report_duration:.2f}秒")
            print(f"   PPT生成时间: {ppt_duration:.2f}秒")

        except Exception as e:
            error_msg = f"报告生成失败: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            step_result = log_step_end(logger, "生成报告", step_start_time, False, error_msg)
            workflow_results['steps']['reporting'] = step_result
            print("⚠️ 报告生成失败")
        
        # 汇总最终结果
        total_duration = time.time() - time.mktime(datetime.fromisoformat(workflow_results['timestamp']).timetuple())

        # 统计成功和失败的步骤
        successful_steps = [name for name, step in workflow_results['steps'].items() if step.get('status') == 'success']
        failed_steps = [name for name, step in workflow_results['steps'].items() if step.get('status') == 'failed']

        # 获取最佳模型信息
        best_model_info = None
        if 'demand_prediction' in workflow_results['steps']:
            demand_step = workflow_results['steps']['demand_prediction']
            if demand_step.get('status') == 'success' and 'best_metrics' in demand_step and demand_step['best_metrics']:
                best_model_info = {
                    'model': demand_step.get('model_used', 'Unknown'),
                    **demand_step['best_metrics']
                }
            else:
                best_model_info = {
                    'model': demand_step.get('model_used', 'Unknown'),
                    'status': 'training_failed'
                }

        # 获取优化结果
        optimization_info = None
        if 'fleet_optimization' in workflow_results['steps'] and 'optimization_stats' in workflow_results['steps']['fleet_optimization']:
            optimization_info = workflow_results['steps']['fleet_optimization']['optimization_stats']

        workflow_results['final_results'] = {
            'overall_status': 'success' if not failed_steps else 'partial_success',
            'total_duration_seconds': round(total_duration, 2),
            'successful_steps': successful_steps,
            'failed_steps': failed_steps,
            'completion_rate': f"{len(successful_steps)}/{len(workflow_results['steps'])}",
            'best_model_performance': best_model_info,
            'optimization_results': optimization_info
        }

        # 记录最终性能指标
        final_memory = log_memory_usage(logger, "工作流完成")
        workflow_results['performance_metrics'] = {
            'total_duration': round(total_duration, 2),
            'final_memory_usage': final_memory,
            'peak_memory_usage': final_memory,  # 简化版本，实际应该跟踪峰值
            'steps_completed': len(successful_steps),
            'steps_failed': len(failed_steps)
        }

        # 保存详细的工作流结果
        results_file = f'results/workflow_execution_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        serializable_results = json_serializable(workflow_results)
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        # 同时保存一个最新版本
        with open('results/workflow_execution_results_latest.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        logger.info("工作流执行完成")
        logger.info(f"总执行时间: {total_duration:.2f}秒")
        logger.info(f"成功步骤: {len(successful_steps)}")
        logger.info(f"失败步骤: {len(failed_steps)}")
        logger.info(f"结果文件: {results_file}")

        print("\n" + "=" * 50)
        if not failed_steps:
            print("🎉 完整工作流执行成功!")
        else:
            print("⚠️ 工作流部分完成!")
            print(f"   失败步骤: {', '.join(failed_steps)}")
        print("=" * 50)
        print(f"🕒 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️ 总执行时间: {total_duration:.2f}秒")
        print(f"✅ 成功步骤: {len(successful_steps)}/{len(workflow_results['steps'])}")

        if best_model_info:
            print(f"📊 最佳模型性能:")
            print(f"   模型: {best_model_info.get('model', 'Unknown')}")
            print(f"   R² Score: {best_model_info.get('r2_score', 0):.4f}")
            print(f"   RMSE: {best_model_info.get('rmse', 0):.2f}")
            print(f"   MAE: {best_model_info.get('mae', 0):.2f}")

        print(f"📂 结果文件保存在:")
        print(f"   - 详细日志: {log_file}")
        print(f"   - 执行结果: {results_file}")
        print(f"   - 最新结果: results/workflow_execution_results_latest.json")

        return workflow_results
        
    except Exception as e:
        total_duration = time.time() - time.mktime(datetime.fromisoformat(workflow_results['timestamp']).timetuple())
        error_msg = f"工作流执行失败: {str(e)}"

        logger.error(error_msg)
        logger.error(traceback.format_exc())

        print(f"\n❌ {error_msg}")
        traceback.print_exc()

        # 统计已完成的步骤
        successful_steps = [name for name, step in workflow_results['steps'].items() if step.get('status') == 'success']
        failed_steps = [name for name, step in workflow_results['steps'].items() if step.get('status') == 'failed']

        workflow_results['final_results'] = {
            'overall_status': 'failed',
            'total_duration_seconds': round(total_duration, 2),
            'successful_steps': successful_steps,
            'failed_steps': failed_steps,
            'completion_rate': f"{len(successful_steps)}/{len(workflow_results['steps'])}",
            'error': str(e),
            'error_traceback': traceback.format_exc()
        }

        # 记录最终性能指标
        final_memory = log_memory_usage(logger, "工作流失败时")
        workflow_results['performance_metrics'] = {
            'total_duration': round(total_duration, 2),
            'final_memory_usage': final_memory,
            'steps_completed': len(successful_steps),
            'steps_failed': len(failed_steps) + 1  # +1 for the current failed step
        }

        # 保存错误结果
        error_results_file = f'results/workflow_execution_results_error_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        serializable_results = json_serializable(workflow_results)
        with open(error_results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        # 同时保存一个最新版本
        with open('results/workflow_execution_results_latest.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        logger.error(f"错误结果保存到: {error_results_file}")

        print(f"📂 错误信息保存在:")
        print(f"   - 详细日志: {log_file}")
        print(f"   - 错误结果: {error_results_file}")

        return workflow_results

def generate_execution_summary():
    """生成执行摘要报告"""
    print("\n" + "=" * 60)
    print("📋 工作流执行摘要报告")
    print("=" * 60)
    
    try:
        # 读取执行结果
        with open('results/workflow_execution_results.json', 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"📅 执行时间: {results['timestamp']}")
        print(f"📊 整体状态: {results['final_results'].get('overall_status', 'unknown')}")
        
        if 'best_model_performance' in results['final_results']:
            perf = results['final_results']['best_model_performance']
            print(f"\n🎯 最佳模型性能:")
            print(f"   模型类型: {perf['model']}")
            print(f"   R² Score: {perf['r2_score']:.4f}")
            print(f"   RMSE: {perf['rmse']:.2f}")
            print(f"   MAE: {perf['mae']:.2f}")
        
        if 'optimization_results' in results['final_results']:
            opt = results['final_results']['optimization_results']
            print(f"\n✈️ 优化结果:")
            print(f"   目标函数值: {opt['objective_value']:,.0f}")
            print(f"   解决方案状态: {opt['status']}")
        
        print(f"\n📝 执行步骤:")
        for step_name, step_info in results['steps'].items():
            status = "✅" if step_info['status'] == 'success' else "❌"
            print(f"   {status} {step_name}: {step_info['status']}")
        
        print(f"\n📂 结果文件:")
        print(f"   详细结果: results/workflow_execution_results.json")
        if os.path.exists('results/reports/fleet_assignment_report.md'):
            print(f"   Markdown报告: results/reports/fleet_assignment_report.md")
        if os.path.exists('results/reports/fleet_assignment_presentation.pptx'):
            print(f"   PPT报告: results/reports/fleet_assignment_presentation.pptx")
        print(f"   最佳模型: models/lstm_model_best.h5")
        
    except Exception as e:
        print(f"❌ 无法生成摘要报告: {str(e)}")

if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/reports', exist_ok=True)
    os.makedirs('results/optimization', exist_ok=True)
    
    # 执行完整工作流
    results = execute_complete_workflow()
    
    # 生成执行摘要
    generate_execution_summary()