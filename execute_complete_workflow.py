"""
完整工作流执行脚本
使用修订后的最佳预测模型执行完整工作流并生成报告
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import json

# 添加src到路径
sys.path.append('src')

def execute_complete_workflow():
    """执行完整的预测工作流"""
    print("=== 航空公司需求预测完整工作流执行 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    workflow_results = {
        'timestamp': datetime.now().isoformat(),
        'steps': {},
        'final_results': {}
    }
    
    try:
        # 步骤1: 数据预处理
        print("\n📋 步骤1: 数据预处理")
        print("-" * 30)
        
        from src.data_preprocessing.preprocessing_pipeline import DataPreprocessingPipeline
        
        # 初始化预处理管道
        pipeline = DataPreprocessingPipeline(data_dir="data")
        
        # 运行预处理
        pipeline.run_pipeline()
        
        # 保存处理后的数据
        pipeline.save_processed_data("results/processed_data")
        
        workflow_results['steps']['data_preprocessing'] = {
            'status': 'success',
            'message': '数据预处理完成',
            'processed_files': [
                'features.csv',
                'schedule.csv', 
                'products.csv',
                'fleet.csv',
                'market_share.csv'
            ]
        }
        
        print("✅ 数据预处理完成")
        print(f"   处理文件数: {len(workflow_results['steps']['data_preprocessing']['processed_files'])}")
        
        # 步骤2: 需求预测
        print("\n📈 步骤2: 需求预测 (使用LSTM最佳模型)")
        print("-" * 30)
        
        from src.demand_prediction.data_preparation import prepare_dataset
        from src.demand_prediction.lstm_model import prepare_lstm_dataset, train_lstm_model
        
        # 准备数据集
        dataset = prepare_dataset("results/processed_data")
        print(f"   数据集准备完成: {len(dataset['X_train'])} 训练样本")
        
        # 准备LSTM数据集
        lstm_dataset = prepare_lstm_dataset(dataset, sequence_length=10)
        print(f"   LSTM序列准备完成: {len(lstm_dataset['X_train'])} 序列")
        
        # 使用修复后的数据训练LSTM模型
        lstm_model, lstm_results = train_lstm_model(lstm_dataset, model_type='LSTM')
        
        # 保存模型
        lstm_model.save_model("models/lstm_model_best.h5")
        
        workflow_results['steps']['demand_prediction'] = {
            'status': 'success',
            'model_used': 'LSTM (修复后最佳模型)',
            'metrics': lstm_results['test_metrics'],
            'model_file': 'lstm_model_best.h5'
        }
        
        print("✅ 需求预测完成")
        print(f"   模型: LSTM (修复后最佳模型)")
        print(f"   R² Score: {lstm_results['test_metrics']['r2_score']:.4f}")
        print(f"   RMSE: {lstm_results['test_metrics']['rmse']:.2f}")
        print(f"   MAE: {lstm_results['test_metrics']['mae']:.2f}")
        
        # 步骤3: 舰队分配优化
        print("\n✈️ 步骤3: 舰队分配优化")
        print("-" * 30)
        
        from src.optimization.fleet_optimizer import FleetOptimizationPipeline
        
        # 初始化优化管道
        opt_pipeline = FleetOptimizationPipeline(data_dir="results/processed_data")
        
        # 运行优化
        opt_results = opt_pipeline.run_pipeline()
        
        # 保存结果
        opt_pipeline.save_results("results/optimization")
        
        workflow_results['steps']['fleet_optimization'] = {
            'status': 'success',
            'message': '舰队分配优化完成',
            'solution_status': opt_results.get('status', 'unknown'),
            'objective_value': opt_results.get('objective_value', 0)
        }
        
        print("✅ 舰队分配优化完成")
        print(f"   解决方案状态: {opt_results.get('status', 'unknown')}")
        print(f"   目标函数值: {opt_results.get('objective_value', 0):,.0f}")
        
        # 步骤4: 分析和评估
        print("\n📊 步骤4: 分析和评估")
        print("-" * 30)
        
        from src.analysis.analysis_pipeline import AnalysisPipeline
        
        # 初始化分析管道
        analysis_pipeline = AnalysisPipeline(data_dir="results/processed_data")
        
        # 运行分析
        analysis_results = analysis_pipeline.run_pipeline()
        
        workflow_results['steps']['analysis'] = {
            'status': 'success',
            'message': '分析评估完成',
            'key_metrics': analysis_results
        }
        
        print("✅ 分析评估完成")
        if analysis_results:
            for key, value in list(analysis_results.items())[:3]:  # 显示前3个关键指标
                print(f"   {key}: {value}")
        
        # 步骤5: 生成报告
        print("\n📄 步骤5: 生成报告")
        print("-" * 30)
        
        from src.reporting.report_generator import ReportGenerator
        
        # 初始化报告生成器
        report_generator = ReportGenerator(results_dir="results")
        
        # 生成完整报告
        markdown_report = report_generator.generate_complete_report()
        
        # 生成PPT演示文稿
        presentation = report_generator.create_presentation()
        
        workflow_results['steps']['reporting'] = {
            'status': 'success',
            'message': '报告生成完成',
            'reports_generated': [
                'fleet_assignment_report.md',
                'fleet_assignment_presentation.pptx'
            ]
        }
        
        print("✅ 报告生成完成")
        print(f"   生成报告数: {len(workflow_results['steps']['reporting']['reports_generated'])}")
        
        # 汇总最终结果
        workflow_results['final_results'] = {
            'overall_status': 'success',
            'best_model_performance': {
                'model': 'LSTM (修复后)',
                'r2_score': lstm_results['test_metrics']['r2_score'],
                'rmse': lstm_results['test_metrics']['rmse'],
                'mae': lstm_results['test_metrics']['mae']
            },
            'optimization_results': {
                'objective_value': opt_results.get('objective_value', 0),
                'status': opt_results.get('status', 'unknown')
            }
        }
        
        # 保存工作流结果
        with open('results/workflow_execution_results.json', 'w', encoding='utf-8') as f:
            json.dump(workflow_results, f, ensure_ascii=False, indent=2)
        
        print("\n" + "=" * 50)
        print("🎉 完整工作流执行成功!")
        print("=" * 50)
        print(f"🕒 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 最佳模型性能 (LSTM):")
        print(f"   R² Score: {lstm_results['test_metrics']['r2_score']:.4f}")
        print(f"   RMSE: {lstm_results['test_metrics']['rmse']:.2f}")
        print(f"   MAE: {lstm_results['test_metrics']['mae']:.2f}")
        print(f"📂 结果文件保存在:")
        print(f"   - 模型: models/lstm_model_best.h5")
        print(f"   - 报告: results/reports/")
        print(f"   - 详细结果: results/workflow_execution_results.json")
        
        return workflow_results
        
    except Exception as e:
        print(f"\n❌ 工作流执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        workflow_results['final_results'] = {
            'overall_status': 'failed',
            'error': str(e)
        }
        
        # 保存错误结果
        with open('results/workflow_execution_results.json', 'w', encoding='utf-8') as f:
            json.dump(workflow_results, f, ensure_ascii=False, indent=2)
        
        return workflow_results

def generate_execution_summary():
    """生成执行摘要报告"""
    print("\n" + "=" * 60)
    print("📋 工作流执行摘要报告")
    print("=" * 60)
    
    try:
        # 读取执行结果
        with open('results/workflow_execution_results.json', 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"📅 执行时间: {results['timestamp']}")
        print(f"📊 整体状态: {results['final_results'].get('overall_status', 'unknown')}")
        
        if 'best_model_performance' in results['final_results']:
            perf = results['final_results']['best_model_performance']
            print(f"\n🎯 最佳模型性能:")
            print(f"   模型类型: {perf['model']}")
            print(f"   R² Score: {perf['r2_score']:.4f}")
            print(f"   RMSE: {perf['rmse']:.2f}")
            print(f"   MAE: {perf['mae']:.2f}")
        
        if 'optimization_results' in results['final_results']:
            opt = results['final_results']['optimization_results']
            print(f"\n✈️ 优化结果:")
            print(f"   目标函数值: {opt['objective_value']:,.0f}")
            print(f"   解决方案状态: {opt['status']}")
        
        print(f"\n📝 执行步骤:")
        for step_name, step_info in results['steps'].items():
            status = "✅" if step_info['status'] == 'success' else "❌"
            print(f"   {status} {step_name}: {step_info['status']}")
        
        print(f"\n📂 结果文件:")
        print(f"   详细结果: results/workflow_execution_results.json")
        if os.path.exists('results/reports/fleet_assignment_report.md'):
            print(f"   Markdown报告: results/reports/fleet_assignment_report.md")
        if os.path.exists('results/reports/fleet_assignment_presentation.pptx'):
            print(f"   PPT报告: results/reports/fleet_assignment_presentation.pptx")
        print(f"   最佳模型: models/lstm_model_best.h5")
        
    except Exception as e:
        print(f"❌ 无法生成摘要报告: {str(e)}")

if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/reports', exist_ok=True)
    os.makedirs('results/optimization', exist_ok=True)
    
    # 执行完整工作流
    results = execute_complete_workflow()
    
    # 生成执行摘要
    generate_execution_summary()