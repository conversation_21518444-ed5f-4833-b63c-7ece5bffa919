"""
Data Preparation Module for Demand Prediction
Handles data splitting, normalization, and feature engineering for ML models
"""
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from typing import Tuple, Dict
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_processed_data(data_dir: str = "results/processed_data") -> Dict[str, pd.DataFrame]:
    """
    Load processed data from preprocessing pipeline
    
    Args:
        data_dir (str): Directory containing processed data files
        
    Returns:
        Dict[str, pd.DataFrame]: Dictionary of loaded dataframes
    """
    logger.info(f"Loading processed data from {data_dir}")
    
    data = {}
    data['features'] = pd.read_csv(f"{data_dir}/features.csv")
    data['schedule'] = pd.read_csv(f"{data_dir}/schedule.csv")
    data['products'] = pd.read_csv(f"{data_dir}/products.csv")
    
    logger.info("Data loaded successfully")
    return data

def prepare_features_for_modeling(feature_df: pd.DataFrame) -> pd.DataFrame:
    """
    Prepare features for machine learning modeling
    
    Args:
        feature_df (pd.DataFrame): Raw feature data
        
    Returns:
        pd.DataFrame: Processed features ready for modeling
    """
    logger.info("Preparing features for modeling")
    
    # Make a copy to avoid modifying original data
    df = feature_df.copy()
    
    # Handle categorical variables
    categorical_columns = ['origin', 'destination', 'flight', 'time_of_day', 'class', 'duration_category']
    categorical_columns = [col for col in categorical_columns if col in df.columns]
    
    # Encode categorical variables
    label_encoders = {}
    for col in categorical_columns:
        if col in df.columns:
            le = LabelEncoder()
            df[col] = le.fit_transform(df[col].astype(str))
            label_encoders[col] = le
    
    # Handle missing values
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df[numeric_columns] = df[numeric_columns].fillna(0)
    
    logger.info(f"Prepared {len(df)} samples with {len(df.columns)} features")
    return df

def create_target_variable(feature_df: pd.DataFrame) -> pd.Series:
    """
    Create target variable (demand) from pre-engineered features
    
    Args:
        feature_df (pd.DataFrame): Pre-engineered feature data with total_bookings
        
    Returns:
        pd.Series: Target variable (total demand)
    """
    logger.info("Creating target variable from pre-engineered features")
    
    # Use the pre-engineered total_bookings as target variable
    if 'total_bookings' in feature_df.columns:
        target = feature_df['total_bookings']
        logger.info(f"Created target variable with {len(target)} samples")
        return target
    else:
        logger.warning("total_bookings column not found, using zeros as fallback")
        return pd.Series([0] * len(feature_df))

def split_data_for_time_series(
    features_df: pd.DataFrame, 
    target: pd.Series, 
    test_size: float = 0.2
) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
    """
    Split data for time series modeling (maintaining temporal order)
    
    Args:
        features_df (pd.DataFrame): Feature data
        target (pd.Series): Target variable
        test_size (float): Proportion of data for testing
        
    Returns:
        Tuple: (X_train, X_test, y_train, y_test)
    """
    logger.info("Splitting data for time series modeling")
    
    # Sort by some temporal indicator if available
    # For now, we'll use a simple random split but maintain the logic
    split_idx = int(len(features_df) * (1 - test_size))
    
    X_train = features_df.iloc[:split_idx]
    X_test = features_df.iloc[split_idx:]
    y_train = target.iloc[:split_idx]
    y_test = target.iloc[split_idx:]
    
    logger.info(f"Train set: {len(X_train)} samples, Test set: {len(X_test)} samples")
    return X_train, X_test, y_train, y_test

def normalize_features(
    X_train: pd.DataFrame, 
    X_test: pd.DataFrame
) -> Tuple[pd.DataFrame, pd.DataFrame, StandardScaler]:
    """
    Normalize features using StandardScaler
    
    Args:
        X_train (pd.DataFrame): Training features
        X_test (pd.DataFrame): Test features
        
    Returns:
        Tuple: (X_train_scaled, X_test_scaled, fitted_scaler)
    """
    logger.info("Normalizing features")
    
    scaler = StandardScaler()
    
    # Fit on training data and transform both sets
    X_train_scaled = pd.DataFrame(
        scaler.fit_transform(X_train), 
        columns=X_train.columns, 
        index=X_train.index
    )
    
    X_test_scaled = pd.DataFrame(
        scaler.transform(X_test), 
        columns=X_test.columns, 
        index=X_test.index
    )
    
    logger.info("Features normalized successfully")
    return X_train_scaled, X_test_scaled, scaler

def prepare_dataset(data_dir: str = "results/processed_data") -> Dict:
    """
    Complete pipeline for preparing dataset for demand prediction
    
    Args:
        data_dir (str): Directory containing processed data
        
    Returns:
        Dict: Prepared dataset with train/test splits and scalers
    """
    logger.info("Starting dataset preparation pipeline")
    
    # Load processed data
    data = load_processed_data(data_dir)
    
    # Prepare features
    features_processed = prepare_features_for_modeling(data['features'])
    
    # Create target variable from pre-engineered features
    target = create_target_variable(data['features'])
    
    # Ensure target and features have same length
    min_length = min(len(features_processed), len(target))
    features_processed = features_processed.iloc[:min_length]
    target = target.iloc[:min_length]
    
    # Split data
    X_train, X_test, y_train, y_test = split_data_for_time_series(
        features_processed, target
    )
    
    # Normalize features
    X_train_scaled, X_test_scaled, scaler = normalize_features(X_train, X_test)
    
    # Prepare result dictionary
    dataset = {
        'X_train': X_train_scaled,
        'X_test': X_test_scaled,
        'y_train': y_train,
        'y_test': y_test,
        'scaler': scaler,
        'feature_names': list(features_processed.columns)
    }
    
    logger.info("Dataset preparation completed successfully")
    return dataset

if __name__ == "__main__":
    # Test the data preparation pipeline
    try:
        dataset = prepare_dataset()
        print("Dataset preparation summary:")
        print(f"Training samples: {len(dataset['X_train'])}")
        print(f"Test samples: {len(dataset['X_test'])}")
        print(f"Features: {len(dataset['feature_names'])}")
        print(f"Training target stats: mean={dataset['y_train'].mean():.2f}, std={dataset['y_train'].std():.2f}")
        print(f"Target variable range: min={dataset['y_train'].min():.2f}, max={dataset['y_train'].max():.2f}")
    except Exception as e:
        logger.error(f"Error in data preparation: {str(e)}")
        raise