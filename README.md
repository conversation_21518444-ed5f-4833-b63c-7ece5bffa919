# 航空公司机型分配优化系统

## 项目概述
A航空公司并购B公司后，为815个每日航班和211架飞机(9种机型)制定优化的机型分配方案，最大化收益并降低成本。

## 🚀 核心调用链

### 主执行文件
- `execute_complete_workflow.py` - 完整工作流执行脚本
- `main.py` - 项目主入口

### 核心模块结构
```
src/
├── data_preprocessing/     # 数据预处理管道
│   ├── preprocessing_pipeline.py
│   ├── data_loader.py
│   ├── timezone_converter.py
│   ├── multi_leg_processor.py
│   └── feature_engineer.py
├── demand_prediction/      # 需求预测模型
│   ├── data_preparation.py
│   ├── lstm_model.py
│   ├── xgboost_model.py
│   └── demand_predictor.py
├── optimization/          # 机型分配优化
│   ├── fleet_optimizer.py
│   ├── model_definition.py
│   └── solver_interface.py
├── analysis/             # 分析评估
│   ├── analysis_pipeline.py
│   ├── financial_analysis.py
│   └── operational_analysis.py
└── reporting/            # 报告生成
    ├── report_generator.py
    ├── visualizations.py
    └── reporting_pipeline.py
```

## 📊 执行流程

### 步骤1: 数据预处理
- 处理4个CSV数据文件
- UTC时间转换
- 经停航班识别
- 特征工程

### 步骤2: 需求预测
- LSTM模型训练 (最佳性能: R²=0.9725)
- XGBoost/GRU模型对比
- 预测结果验证

### 步骤3: 机型分配优化
- MILP模型构建
- 约束条件设置
- 优化求解

### 步骤4: 分析评估
- 财务分析
- 运营效率分析
- 敏感性分析

### 步骤5: 报告生成
- Markdown报告
- PPT演示文稿
- 可视化图表

## 📁 数据文件
```
data/
├── data_fam_schedule.csv    # 航班时刻表
├── data_fam_fleet.csv       # 机队信息
├── data_fam_products.csv    # 产品销售历史
└── data_fam_market_share.csv # 市场份额数据
```

## 🎯 核心成果
- **最佳模型**: LSTM (R²=0.9725, RMSE=8.76, MAE=6.45)
- **模型文件**: `models/lstm_model_best.h5`
- **处理数据**: `results/processed_data/`
- **执行结果**: `results/workflow_execution_results.json`

## 📚 归档文件
```
archive/
├── backup/              # 备份文件
├── documentation/       # 项目文档
├── outdated_models/     # 过时模型
├── standalone_scripts/  # 独立脚本
└── test_files/         # 测试文件
```

## 🔧 环境要求
- Python 3.8+
- 依赖包: `pip install -r requirements.txt`

## 📈 项目状态
- ✅ 数据预处理完成
- ✅ 需求预测模型训练完成 (LSTM最佳)
- ⚠️ 机型分配优化待完成
- ⚠️ 分析评估待完成
- ⚠️ 最终报告生成待完成

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖包
pip install -r requirements.txt

# 测试增强版工作流
python test_enhanced_workflow.py
```

### 2. 执行工作流
```bash
# 执行完整工作流（增强版，包含详细日志）
python execute_complete_workflow.py

# 或运行主程序（基础版）
python main.py
```

### 3. 分析执行结果
```bash
# 生成性能分析报告
python analyze_workflow_logs.py

# 列出可用的日志文件
python analyze_workflow_logs.py --list
```

## 📊 日志和监控功能

### 详细日志记录
- **系统信息**: 平台、Python版本、CPU、内存
- **步骤执行**: 每个步骤的开始/结束时间、执行时长
- **内存监控**: 实时内存使用情况跟踪
- **模型训练**: 详细的训练过程和性能指标
- **错误处理**: 完整的错误信息和堆栈跟踪

### 日志文件位置
```
results/
├── logs/                           # 详细执行日志
│   └── workflow_execution_*.log
├── workflow_execution_results_*.json  # 结构化执行结果
└── workflow_execution_results_latest.json  # 最新执行结果
```

### 性能分析
- 步骤执行时间分析
- 模型性能对比
- 内存使用统计
- 优化结果评估
- 改进建议生成

## 📞 技术支持
项目基于机器学习和运筹学方法，实现航空公司机型分配的智能优化。增强版本提供完整的执行监控和性能分析功能。
