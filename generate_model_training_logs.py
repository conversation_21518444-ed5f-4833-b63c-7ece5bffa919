"""
Model Training Logger and Visualizer
Records detailed training logs and generates visualization charts for all models
"""
import os
import json
import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelTrainingLogger:
    """
    Logger for recording detailed model training information and generating visualizations
    """
    
    def __init__(self, log_dir: str = "results/model_logs", figure_dir: str = "results/reports/figures"):
        """
        Initialize the model training logger
        
        Args:
            log_dir (str): Directory to save training logs
            figure_dir (str): Directory to save visualization figures
        """
        self.log_dir = log_dir
        self.figure_dir = figure_dir
        self.training_logs = {}
        
        # Create directories
        os.makedirs(self.log_dir, exist_ok=True)
        os.makedirs(self.figure_dir, exist_ok=True)
        
        logger.info(f"ModelTrainingLogger initialized with log_dir={log_dir}, figure_dir={figure_dir}")
    
    def record_training_session(self, model_name: str, history: Dict[str, Any], 
                              metrics: Dict[str, Any] = None, params: Dict[str, Any] = None):
        """
        Record a complete training session
        
        Args:
            model_name (str): Name of the model
            history (Dict): Training history (loss, metrics over epochs)
            metrics (Dict): Final evaluation metrics
            params (Dict): Model parameters and configuration
        """
        session_data = {
            'model_name': model_name,
            'timestamp': datetime.now().isoformat(),
            'history': self._serialize_history(history),
            'metrics': metrics or {},
            'params': params or {}
        }
        
        self.training_logs[model_name] = session_data
        
        # Save to file
        log_file = os.path.join(self.log_dir, f"{model_name}_training_session.json")
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Recorded training session for {model_name} to {log_file}")
    
    def _serialize_history(self, history: Dict[str, Any]) -> Dict[str, Any]:
        """
        Serialize training history for JSON storage
        
        Args:
            history (Dict): Training history dictionary
            
        Returns:
            Dict: Serializable history dictionary
        """
        serialized = {}
        for key, value in history.items():
            if isinstance(value, (np.ndarray, list)):
                serialized[key] = list(value)
            elif isinstance(value, (int, float, str, bool)):
                serialized[key] = value
            else:
                serialized[key] = str(value)
        return serialized
    
    def load_training_logs(self):
        """
        Load all existing training logs from log directory
        """
        self.training_logs = {}
        
        if os.path.exists(self.log_dir):
            for file in os.listdir(self.log_dir):
                if file.endswith('_training_session.json'):
                    filepath = os.path.join(self.log_dir, file)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            log_data = json.load(f)
                            model_name = log_data['model_name']
                            self.training_logs[model_name] = log_data
                            logger.info(f"Loaded training log for {model_name}")
                    except Exception as e:
                        logger.warning(f"Failed to load training log {file}: {str(e)}")
    
    def generate_training_charts(self):
        """
        Generate training progress charts for all recorded models
        """
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # Set style
            plt.style.use('seaborn-v0_8')
            sns.set_palette("husl")
            
            # Generate charts for each model
            for model_name, log_data in self.training_logs.items():
                self._generate_model_chart(model_name, log_data)
            
            logger.info(f"Generated training charts for {len(self.training_logs)} models")
            
        except ImportError:
            logger.warning("matplotlib/seaborn not available, skipping chart generation")
        except Exception as e:
            logger.error(f"Error generating training charts: {str(e)}")
    
    def _generate_model_chart(self, model_name: str, log_data: Dict[str, Any]):
        """
        Generate training progress chart for a single model
        
        Args:
            model_name (str): Name of the model
            log_data (Dict): Training log data
        """
        try:
            import matplotlib.pyplot as plt
            
            history = log_data.get('history', {})
            if not history:
                logger.warning(f"No history data found for {model_name}")
                return
            
            # Create figure
            fig, ax1 = plt.subplots(figsize=(12, 8))
            
            # Plot training loss
            if 'loss' in history:
                epochs = range(1, len(history['loss']) + 1)
                ax1.plot(epochs, history['loss'], 'b-', label='Training Loss', linewidth=2)
                ax1.set_ylabel('Loss', color='blue')
                ax1.tick_params(axis='y', labelcolor='blue')
                
                # Plot validation loss if available
                if 'val_loss' in history:
                    ax1.plot(epochs, history['val_loss'], 'b--', label='Validation Loss', linewidth=2)
            
            # Create second y-axis for MAE if available
            ax2 = None
            if 'mae' in history:
                ax2 = ax1.twinx()
                epochs = range(1, len(history['mae']) + 1)
                ax2.plot(epochs, history['mae'], 'r-', label='Training MAE', linewidth=2)
                ax2.set_ylabel('MAE', color='red')
                ax2.tick_params(axis='y', labelcolor='red')
                
                if 'val_mae' in history:
                    ax2.plot(epochs, history['val_mae'], 'r--', label='Validation MAE', linewidth=2)
            
            # Labels and formatting
            plt.title(f'{model_name} Training Progress', fontsize=16, pad=20)
            ax1.set_xlabel('Epochs')
            
            # Legend
            lines1, labels1 = ax1.get_legend_handles_labels()
            if ax2:
                lines2, labels2 = ax2.get_legend_handles_labels()
                ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
            else:
                ax1.legend(loc='upper left')
            
            ax1.grid(True, alpha=0.3)
            
            # Save figure
            filepath = os.path.join(self.figure_dir, f"{model_name}_training_progress.png")
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved training progress chart for {model_name} to {filepath}")
            
        except Exception as e:
            logger.error(f"Error generating chart for {model_name}: {str(e)}")
    
    def generate_model_comparison_chart(self):
        """
        Generate comparison chart showing performance of all models
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np
            
            if not self.training_logs:
                logger.warning("No training logs available for comparison")
                return
            
            # Collect model metrics
            model_names = []
            r2_scores = []
            rmse_scores = []
            
            for model_name, log_data in self.training_logs.items():
                metrics = log_data.get('metrics', {})
                if 'r2_score' in metrics and 'rmse' in metrics:
                    model_names.append(model_name)
                    r2_scores.append(metrics['r2_score'])
                    rmse_scores.append(metrics['rmse'])
            
            if not model_names:
                logger.warning("No comparable metrics found in training logs")
                return
            
            # Create comparison chart
            fig, ax1 = plt.subplots(figsize=(12, 8))
            
            # Bar chart for R² scores
            x = np.arange(len(model_names))
            width = 0.35
            
            bars1 = ax1.bar(x - width/2, r2_scores, width, label='R² Score', color='green', alpha=0.7)
            ax1.set_ylabel('R² Score', color='green')
            ax1.set_ylim(0, 1)
            
            # Add value labels for R²
            for bar, score in zip(bars1, r2_scores):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                        f'{score:.3f}', ha='center', va='bottom')
            
            # Secondary y-axis for RMSE
            ax2 = ax1.twinx()
            bars2 = ax2.bar(x + width/2, rmse_scores, width, label='RMSE', color='red', alpha=0.7)
            ax2.set_ylabel('RMSE', color='red')
            
            # Add value labels for RMSE
            for bar, score in zip(bars2, rmse_scores):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{score:.1f}', ha='center', va='bottom')
            
            # Labels and formatting
            ax1.set_xlabel('Models')
            ax1.set_title('Model Performance Comparison', fontsize=16, pad=20)
            ax1.set_xticks(x)
            ax1.set_xticklabels(model_names, rotation=45, ha="right")
            ax1.legend(loc='upper left')
            ax2.legend(loc='upper right')
            ax1.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # Save figure
            filepath = os.path.join(self.figure_dir, "model_performance_comparison.png")
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved model comparison chart to {filepath}")
            
        except Exception as e:
            logger.error(f"Error generating model comparison chart: {str(e)}")
    
    def generate_detailed_report(self, output_file: str = "results/model_training_detailed_report.md"):
        """
        Generate detailed report of all model training sessions
        
        Args:
            output_file (str): Path to save detailed report
        """
        report_lines = []
        report_lines.append("# Detailed Model Training Report")
        report_lines.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        if not self.training_logs:
            report_lines.append("No training logs available.")
            report_lines.append("")
        else:
            report_lines.append(f"## Summary")
            report_lines.append(f"Total models trained: {len(self.training_logs)}")
            report_lines.append("")
            
            for model_name, log_data in self.training_logs.items():
                report_lines.append(f"## {model_name}")
                report_lines.append(f"Training completed: {log_data.get('timestamp', 'Unknown')}")
                report_lines.append("")
                
                # Parameters
                params = log_data.get('params', {})
                if params:
                    report_lines.append("### Model Parameters")
                    for key, value in params.items():
                        report_lines.append(f"- {key}: {value}")
                    report_lines.append("")
                
                # Metrics
                metrics = log_data.get('metrics', {})
                if metrics:
                    report_lines.append("### Performance Metrics")
                    for key, value in metrics.items():
                        if isinstance(value, (int, float)):
                            report_lines.append(f"- {key}: {value:.4f}")
                        else:
                            report_lines.append(f"- {key}: {value}")
                    report_lines.append("")
                
                # Training history summary
                history = log_data.get('history', {})
                if history:
                    report_lines.append("### Training History Summary")
                    for key, values in history.items():
                        if isinstance(values, (list, np.ndarray)) and len(values) > 0:
                            final_value = values[-1]
                            report_lines.append(f"- Final {key}: {final_value:.6f}")
                    report_lines.append("")
        
        # Save report
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        logger.info(f"Saved detailed training report to {output_file}")

def simulate_training_logs():
    """
    Simulate training logs for demonstration purposes
    """
    logger = ModelTrainingLogger()
    
    # Simulate LSTM training
    lstm_history = {
        'loss': [100.0, 80.0, 60.0, 45.0, 35.0, 28.0, 22.0, 18.0, 15.0, 12.5, 10.8, 9.2, 7.8, 6.5, 5.3],
        'mae': [8.5, 7.2, 6.0, 5.2, 4.5, 4.0, 3.6, 3.2, 2.9, 2.6, 2.4, 2.2, 2.0, 1.8, 1.7],
        'val_loss': [110.0, 85.0, 65.0, 50.0, 40.0, 32.0, 26.0, 21.0, 17.5, 15.0, 13.2, 11.5, 10.0, 8.7, 7.5],
        'val_mae': [9.0, 7.5, 6.3, 5.5, 4.8, 4.2, 3.8, 3.4, 3.0, 2.7, 2.5, 2.3, 2.1, 1.9, 1.8]
    }
    
    lstm_metrics = {
        'r2_score': 0.9725,
        'rmse': 8.76,
        'mae': 6.45,
        'mape': 10.23
    }
    
    lstm_params = {
        'model_type': 'LSTM',
        'layers': [50, 50],
        'dropout_rate': 0.2,
        'learning_rate': 0.001,
        'epochs': 50,
        'batch_size': 32
    }
    
    logger.record_training_session('LSTM_Model', lstm_history, lstm_metrics, lstm_params)
    
    # Simulate GRU training
    gru_history = {
        'loss': [95.0, 75.0, 58.0, 42.0, 32.0, 26.0, 20.0, 16.5, 13.8, 11.5, 9.8, 8.2, 7.0, 5.8, 4.9],
        'mae': [8.2, 6.9, 5.8, 5.0, 4.3, 3.8, 3.4, 3.0, 2.7, 2.4, 2.2, 2.0, 1.8, 1.6, 1.5],
        'val_loss': [105.0, 80.0, 62.0, 47.0, 37.0, 29.0, 23.0, 19.0, 16.2, 13.8, 12.0, 10.3, 8.8, 7.5, 6.3],
        'val_mae': [8.7, 7.2, 6.0, 5.2, 4.6, 4.0, 3.6, 3.2, 2.8, 2.5, 2.3, 2.1, 1.9, 1.7, 1.6]
    }
    
    gru_metrics = {
        'r2_score': 0.9681,
        'rmse': 9.12,
        'mae': 6.89,
        'mape': 11.67
    }
    
    gru_params = {
        'model_type': 'GRU',
        'layers': [50, 50],
        'dropout_rate': 0.2,
        'learning_rate': 0.001,
        'epochs': 50,
        'batch_size': 32
    }
    
    logger.record_training_session('GRU_Model', gru_history, gru_metrics, gru_params)
    
    # Simulate XGBoost training (no history for tree-based models)
    xgb_metrics = {
        'r2_score': 0.8662,
        'rmse': 21.45,
        'mae': 15.23,
        'mape': 18.45
    }
    
    xgb_params = {
        'model_type': 'XGBoost',
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1,
        'subsample': 1.0
    }
    
    logger.record_training_session('XGBoost_Model', {}, xgb_metrics, xgb_params)
    
    # Generate visualizations
    logger.generate_training_charts()
    logger.generate_model_comparison_chart()
    logger.generate_detailed_report()
    
    logger.load_training_logs()
    logger.generate_detailed_report("results/model_training_final_report.md")
    
    print("✅ Simulated training logs generated successfully!")
    print("📁 Files created:")
    print("   - results/model_logs/*.json")
    print("   - results/reports/figures/*_training_progress.png")
    print("   - results/reports/figures/model_performance_comparison.png")
    print("   - results/model_training_detailed_report.md")
    print("   - results/model_training_final_report.md")

if __name__ == "__main__":
    simulate_training_logs()