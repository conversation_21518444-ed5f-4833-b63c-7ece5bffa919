# 归档文件说明

本目录包含项目开发过程中产生的非核心文件，为保持主项目结构清晰而移动至此。

## 📁 目录结构

### backup/
- `backup_20250730/` - 2025年7月30日的代码备份
  - 包含早期版本的数据准备、需求预测、LSTM和XGBoost模型文件

### documentation/
项目相关文档和报告文件：
- `CLAUDE.md` - Claude AI助手的使用说明
- `execution_summary.txt` - 执行摘要
- `final_comprehensive_report.md` - 最终综合报告
- `model_training_detailed_report.md` - 模型训练详细报告
- `model_training_final_report.md` - 模型训练最终报告
- `project_final_summary.md` - 项目最终总结
- `task.docx` - 任务文档
- `航班机型分配优化方案_.docx` - 优化方案文档
- `项目说明书.md` - 项目说明书

### outdated_models/
过时的模型文件：
- `gru_model.h5` / `gru_model_fixed.h5` - GRU模型文件
- `lstm_model.h5` / `lstm_model_fixed.h5` - 早期LSTM模型文件
- `xgboost_model.joblib` / `xgboost_model_fixed.joblib` - XGBoost模型文件
- `model_results_comparison.csv` - 模型结果对比
- `model_training_report.csv` / `model_training_report.md` - 模型训练报告

### standalone_scripts/
独立运行的脚本文件：
- `cleanup_outdated_files.py` - 清理过时文件
- `complete_and_report.py` - 完成并生成报告
- `generate_model_training_logs.py` - 生成模型训练日志
- `generate_report.py` - 生成报告
- `generate_updated_reports.py` - 生成更新报告
- `quick_data_check.py` - 快速数据检查
- `train_and_save_models.py` - 训练并保存模型
- `update_models.py` - 更新模型

### test_files/
测试相关文件：
- `test_data/` - 测试数据目录
- `tests/` - 测试脚本目录

## 🔄 文件恢复
如需恢复任何归档文件到主项目目录，可以使用以下命令：
```bash
# 恢复特定文件
cp archive/path/to/file /path/to/destination

# 恢复整个目录
cp -r archive/directory_name /path/to/destination
```

## 📝 注意事项
- 归档文件仅作为历史记录保存
- 当前项目使用的是主目录中的最新版本文件
- 归档文件可能包含过时的代码或配置，使用时请注意版本兼容性

## 🗑️ 清理建议
如果确认不再需要某些归档文件，可以安全删除以节省存储空间。建议保留：
- 最新的文档文件
- 重要的模型训练记录
- 关键的备份文件
