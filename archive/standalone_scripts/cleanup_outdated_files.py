"""
Cleanup Script for Outdated Python Files
Removes old test files and backup files that are no longer needed
"""
import os
import shutil
import logging
from typing import List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_outdated_files():
    """
    Clean up outdated Python files and test scripts
    """
    # Files to remove (outdated test files)
    files_to_remove = [
        "src/demand_prediction/test_models.py",
        "src/demand_prediction/test_lstm_cpu.py", 
        "src/demand_prediction/quick_test.py",
        "src/reporting/test_reporting.py",
        "src/analysis/test_analysis.py",
        "src/optimization/test_optimizer.py",
        "src/feature_analysis/test_feature_analysis.py"
    ]
    
    # Directories to remove (cache directories)
    dirs_to_remove = [
        "src/demand_prediction/__pycache__",
        "src/reporting/__pycache__",
        "src/analysis/__pycache__",
        "src/optimization/__pycache__",
        "src/feature_analysis/__pycache__"
    ]
    
    # Remove files
    removed_files = []
    failed_files = []
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                logger.info(f"Removed file: {file_path}")
            except Exception as e:
                failed_files.append((file_path, str(e)))
                logger.warning(f"Failed to remove file {file_path}: {str(e)}")
        else:
            logger.debug(f"File not found (already removed): {file_path}")
    
    # Remove directories
    removed_dirs = []
    failed_dirs = []
    
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                removed_dirs.append(dir_path)
                logger.info(f"Removed directory: {dir_path}")
            except Exception as e:
                failed_dirs.append((dir_path, str(e)))
                logger.warning(f"Failed to remove directory {dir_path}: {str(e)}")
        else:
            logger.debug(f"Directory not found (already removed): {dir_path}")
    
    # Summary
    print("\n=== Cleanup Summary ===")
    print(f"Files removed: {len(removed_files)}")
    for file_path in removed_files:
        print(f"  ✅ {file_path}")
    
    print(f"Directories removed: {len(removed_dirs)}")
    for dir_path in removed_dirs:
        print(f"  ✅ {dir_path}")
    
    if failed_files:
        print(f"Files failed to remove: {len(failed_files)}")
        for file_path, error in failed_files:
            print(f"  ❌ {file_path}: {error}")
    
    if failed_dirs:
        print(f"Directories failed to remove: {len(failed_dirs)}")
        for dir_path, error in failed_dirs:
            print(f"  ❌ {dir_path}: {error}")
    
    if not failed_files and not failed_dirs:
        print("\n🎉 All cleanup operations completed successfully!")
    
    return {
        'removed_files': removed_files,
        'failed_files': failed_files,
        'removed_dirs': removed_dirs,
        'failed_dirs': failed_dirs
    }

def backup_important_files():
    """
    Backup any important files before cleanup (just in case)
    """
    important_files = [
        "src/demand_prediction/data_preparation.py",
        "src/demand_prediction/xgboost_model.py",
        "src/demand_prediction/lstm_model.py",
        "src/demand_prediction/demand_predictor.py"
    ]
    
    backup_dir = "backup_20250730"
    os.makedirs(backup_dir, exist_ok=True)
    
    backed_up = []
    failed_backup = []
    
    for file_path in important_files:
        if os.path.exists(file_path):
            try:
                filename = os.path.basename(file_path)
                backup_path = os.path.join(backup_dir, filename)
                shutil.copy2(file_path, backup_path)
                backed_up.append(file_path)
                logger.info(f"Backed up: {file_path} -> {backup_path}")
            except Exception as e:
                failed_backup.append((file_path, str(e)))
                logger.warning(f"Failed to backup {file_path}: {str(e)}")
    
    print(f"\n=== Backup Summary ===")
    print(f"Files backed up: {len(backed_up)}")
    for file_path in backed_up:
        print(f"  📦 {file_path}")
    
    if failed_backup:
        print(f"Files failed to backup: {len(failed_backup)}")
        for file_path, error in failed_backup:
            print(f"  ❌ {file_path}: {error}")
    
    return {
        'backed_up': backed_up,
        'failed_backup': failed_backup
    }

if __name__ == "__main__":
    print("🧹 Starting Python file cleanup process...")
    
    # Backup important files first
    backup_results = backup_important_files()
    
    # Clean up outdated files
    cleanup_results = cleanup_outdated_files()
    
    print(f"\n📊 Final Summary:")
    print(f"✅ Backed up: {len(backup_results['backed_up'])} files")
    print(f"✅ Removed: {len(cleanup_results['removed_files'])} files, {len(cleanup_results['removed_dirs'])} directories")
    print(f"❌ Failed: {len(backup_results['failed_backup']) + len(cleanup_results['failed_files']) + len(cleanup_results['failed_dirs'])} operations")
    
    print(f"\n📁 Backup files are stored in: backup_20250730/")
    print(f"🧹 Cleanup completed!")