"""
Main Demand Prediction Pipeline
Orchestrates the complete demand prediction workflow
"""
import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, <PERSON><PERSON>, Optional

from .data_preparation import prepare_dataset
from .xgboost_model import XGBoostDemandPredictor, train_xgboost_model
from .lstm_model import LSTMDemandPredictor, prepare_lstm_dataset, train_lstm_model

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DemandPredictionPipeline:
    """
    Main pipeline for airline demand prediction
    """
    
    def __init__(self, data_dir: str = "results/processed_data"):
        """
        Initialize the demand prediction pipeline
        
        Args:
            data_dir (str): Directory containing processed data
        """
        self.data_dir = data_dir
        self.dataset = None
        self.lstm_dataset = None
        self.xgboost_model = None
        self.lstm_model = None
        self.gru_model = None
        self.results = {}
    
    def prepare_data(self) -> Dict:
        """
        Prepare dataset for demand prediction
        
        Returns:
            Dict: Prepared dataset
        """
        logger.info("Preparing dataset for demand prediction")
        self.dataset = prepare_dataset(self.data_dir)
        logger.info("Dataset preparation completed")
        return self.dataset
    
    def train_xgboost(self, hyperparameter_tuning: bool = False) -> Tuple[XGBoostDemandPredictor, Dict]:
        """
        Train XGBoost model
        
        Args:
            hyperparameter_tuning (bool): Whether to perform hyperparameter tuning
            
        Returns:
            Tuple: (trained_model, training_results)
        """
        if self.dataset is None:
            raise ValueError("Dataset not prepared. Call prepare_data() first.")
            
        logger.info("Training XGBoost model")
        self.xgboost_model, results = train_xgboost_model(self.dataset, hyperparameter_tuning)
        self.results['xgboost'] = results
        logger.info("XGBoost model training completed")
        return self.xgboost_model, results
    
    def train_lstm(self, sequence_length: int = 10) -> Tuple[LSTMDemandPredictor, Dict]:
        """
        Train LSTM model
        
        Args:
            sequence_length (int): Length of sequences for LSTM
            
        Returns:
            Tuple: (trained_model, training_results)
        """
        if self.dataset is None:
            raise ValueError("Dataset not prepared. Call prepare_data() first.")
            
        logger.info("Training LSTM model")
        
        # Prepare LSTM dataset
        self.lstm_dataset = prepare_lstm_dataset(self.dataset, sequence_length)
        
        # Train LSTM model
        self.lstm_model, results = train_lstm_model(self.lstm_dataset, 'LSTM')
        self.results['lstm'] = results
        logger.info("LSTM model training completed")
        return self.lstm_model, results
    
    def train_gru(self, sequence_length: int = 10) -> Tuple[LSTMDemandPredictor, Dict]:
        """
        Train GRU model
        
        Args:
            sequence_length (int): Length of sequences for GRU
            
        Returns:
            Tuple: (trained_model, training_results)
        """
        if self.dataset is None:
            raise ValueError("Dataset not prepared. Call prepare_data() first.")
            
        logger.info("Training GRU model")
        
        # Prepare LSTM dataset if not already done
        if self.lstm_dataset is None:
            self.lstm_dataset = prepare_lstm_dataset(self.dataset, sequence_length)
        
        # Train GRU model
        self.gru_model, results = train_lstm_model(self.lstm_dataset, 'GRU')
        self.results['gru'] = results
        logger.info("GRU model training completed")
        return self.gru_model, results
    
    def compare_models(self) -> pd.DataFrame:
        """
        Compare performance of all trained models
        
        Returns:
            pd.DataFrame: Comparison of model performance
        """
        logger.info("Comparing model performance")
        
        comparison_data = []
        
        # XGBoost results
        if 'xgboost' in self.results:
            xgb_metrics = self.results['xgboost']['test_metrics']
            comparison_data.append({
                'model': 'XGBoost',
                'r2_score': xgb_metrics['r2_score'],
                'rmse': xgb_metrics['rmse'],
                'mae': xgb_metrics['mae'],
                'mape': xgb_metrics['mape']
            })
        
        # LSTM results
        if 'lstm' in self.results:
            lstm_metrics = self.results['lstm']['test_metrics']
            comparison_data.append({
                'model': 'LSTM',
                'r2_score': lstm_metrics['r2_score'],
                'rmse': lstm_metrics['rmse'],
                'mae': lstm_metrics['mae'],
                'mape': lstm_metrics['mape']
            })
        
        # GRU results
        if 'gru' in self.results:
            gru_metrics = self.results['gru']['test_metrics']
            comparison_data.append({
                'model': 'GRU',
                'r2_score': gru_metrics['r2_score'],
                'rmse': gru_metrics['rmse'],
                'mae': gru_metrics['mae'],
                'mape': gru_metrics['mape']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('r2_score', ascending=False)
        
        logger.info("Model comparison completed")
        return comparison_df
    
    def get_best_model(self) -> Tuple[str, object]:
        """
        Get the best performing model based on R² score
        
        Returns:
            Tuple: (model_name, model_object)
        """
        comparison_df = self.compare_models()
        
        if len(comparison_df) == 0:
            raise ValueError("No models have been trained yet")
        
        best_model_name = comparison_df.iloc[0]['model']
        
        if best_model_name == 'XGBoost':
            return best_model_name, self.xgboost_model
        elif best_model_name == 'LSTM':
            return best_model_name, self.lstm_model
        elif best_model_name == 'GRU':
            return best_model_name, self.gru_model
        else:
            raise ValueError(f"Unknown model type: {best_model_name}")
    
    def save_models(self, models_dir: str = "models"):
        """
        Save all trained models
        
        Args:
            models_dir (str): Directory to save models
        """
        logger.info(f"Saving models to {models_dir}")
        
        # Create directory if it doesn't exist
        os.makedirs(models_dir, exist_ok=True)
        
        # Save XGBoost model
        if self.xgboost_model is not None:
            xgb_path = os.path.join(models_dir, "xgboost_model.joblib")
            self.xgboost_model.save_model(xgb_path)
        
        # Save LSTM model
        if self.lstm_model is not None:
            lstm_path = os.path.join(models_dir, "lstm_model.h5")
            self.lstm_model.save_model(lstm_path)
        
        # Save GRU model
        if self.gru_model is not None:
            gru_path = os.path.join(models_dir, "gru_model.h5")
            self.gru_model.save_model(gru_path)
        
        # Save results
        results_path = os.path.join(models_dir, "model_results.csv")
        comparison_df = self.compare_models()
        comparison_df.to_csv(results_path, index=False)
        
        logger.info("Models saved successfully")
    
    def run_pipeline(self, train_xgboost: bool = True, train_lstm: bool = True, 
                     train_gru: bool = True, hyperparameter_tuning: bool = False) -> Dict:
        """
        Run the complete demand prediction pipeline
        
        Args:
            train_xgboost (bool): Whether to train XGBoost model
            train_lstm (bool): Whether to train LSTM model
            train_gru (bool): Whether to train GRU model
            hyperparameter_tuning (bool): Whether to perform hyperparameter tuning for XGBoost
            
        Returns:
            Dict: Complete pipeline results
        """
        logger.info("Starting demand prediction pipeline")
        
        # Prepare data
        self.prepare_data()
        
        # Train models
        if train_xgboost:
            self.train_xgboost(hyperparameter_tuning)
        
        if train_lstm:
            self.train_lstm()
        
        if train_gru:
            self.train_gru()
        
        # Compare models
        comparison_df = self.compare_models()
        
        # Get best model
        try:
            best_model_name, best_model = self.get_best_model()
            logger.info(f"Best model: {best_model_name}")
        except ValueError:
            best_model_name, best_model = None, None
            logger.warning("No models were trained successfully")
        
        # Save models
        self.save_models()
        
        # Prepare final results
        pipeline_results = {
            'comparison': comparison_df,
            'best_model_name': best_model_name,
            'best_model': best_model,
            'individual_results': self.results
        }
        
        logger.info("Demand prediction pipeline completed successfully")
        return pipeline_results

def main():
    """
    Main function to run the demand prediction pipeline
    """
    # Initialize pipeline
    pipeline = DemandPredictionPipeline()
    
    try:
        # Run the complete pipeline
        results = pipeline.run_pipeline(
            train_xgboost=True,
            train_lstm=True,
            train_gru=True,
            hyperparameter_tuning=False
        )
        
        # Print results
        print("\n=== Demand Prediction Results ===")
        print(results['comparison'].to_string(index=False))
        
        if results['best_model_name']:
            print(f"\nBest model: {results['best_model_name']}")
            
            # Show feature importance for XGBoost
            if results['best_model_name'] == 'XGBoost' and 'feature_importance' in results['individual_results']['xgboost']:
                print("\nTop 10 Feature Importances:")
                feature_imp = results['individual_results']['xgboost']['feature_importance']
                print(feature_imp.head(10).to_string(index=False))
        
        print("\nModels saved to 'models' directory")
        print("Pipeline completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in demand prediction pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()