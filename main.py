"""
Main Orchestration Script
Runs the complete airline fleet assignment optimization workflow
"""
import os
import sys
import logging
from datetime import datetime

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_data_preprocessing():
    """Run data preprocessing pipeline"""
    logger.info("=== Step 1: Running Data Preprocessing ===")
    
    try:
        from src.data_preprocessing.preprocessing_pipeline import DataPreprocessingPipeline
        
        # Initialize pipeline
        pipeline = DataPreprocessingPipeline(data_dir="data")
        
        # Run preprocessing
        pipeline.run_pipeline()
        
        # Save results
        pipeline.save_processed_data("results/processed_data")
        
        logger.info("✅ Data preprocessing completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in data preprocessing: {str(e)}")
        return False

def run_demand_prediction():
    """Run demand prediction models"""
    logger.info("=== Step 2: Running Demand Prediction ===")
    
    try:
        from src.demand_prediction.demand_predictor import DemandPredictionPipeline
        
        # Initialize pipeline
        pipeline = DemandPredictionPipeline(data_dir="results/processed_data")
        
        # Run pipeline with all models
        results = pipeline.run_pipeline(
            train_xgboost=True,
            train_lstm=True,
            train_gru=True,
            hyperparameter_tuning=False
        )
        
        logger.info("✅ Demand prediction completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in demand prediction: {str(e)}")
        return False

def run_optimization():
    """Run fleet assignment optimization"""
    logger.info("=== Step 3: Running Fleet Assignment Optimization ===")
    
    try:
        from src.optimization.fleet_optimizer import FleetOptimizationPipeline
        
        # Initialize pipeline
        pipeline = FleetOptimizationPipeline(
            data_dir="results/processed_data"
        )
        
        # Run optimization
        results = pipeline.run_pipeline()
        
        # Save results
        pipeline.save_results("results/optimization")
        
        logger.info("✅ Fleet assignment optimization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in fleet assignment optimization: {str(e)}")
        return False

def run_analysis():
    """Run financial and operational analysis"""
    logger.info("=== Step 4: Running Analysis Pipeline ===")
    
    try:
        from src.analysis.analysis_pipeline import AnalysisPipeline
        
        # Initialize pipeline
        pipeline = AnalysisPipeline(data_dir="results/processed_data")
        
        # Run analysis
        results = pipeline.run_pipeline()
        
        logger.info("✅ Analysis pipeline completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in analysis pipeline: {str(e)}")
        return False

def run_feature_analysis():
    """Run feature importance analysis"""
    logger.info("=== Step 5: Running Feature Importance Analysis ===")
    
    try:
        from src.feature_analysis.main import main as run_feature_analysis_main
        
        # Run feature analysis
        run_feature_analysis_main()
        
        logger.info("✅ Feature importance analysis completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in feature importance analysis: {str(e)}")
        return False

def run_reporting():
    """Run automated reporting"""
    logger.info("=== Step 6: Running Automated Reporting ===")
    
    try:
        from src.reporting.report_generator import ReportGenerator
        
        # Initialize report generator
        generator = ReportGenerator(results_dir="results")
        
        # Generate markdown report
        markdown_report = generator.generate_complete_report()
        
        # Generate PowerPoint presentation
        presentation = generator.create_presentation()
        
        logger.info("✅ Automated reporting completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in automated reporting: {str(e)}")
        return False

def main():
    """Main orchestration function"""
    logger.info("🚀 Starting Airline Fleet Assignment Optimization Workflow")
    logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create results directory
    os.makedirs("results", exist_ok=True)
    os.makedirs("results/analysis", exist_ok=True)
    os.makedirs("results/optimization", exist_ok=True)
    os.makedirs("results/reports", exist_ok=True)
    
    # Run workflow steps
    steps = [
        ("Data Preprocessing", run_data_preprocessing),
        ("Demand Prediction", run_demand_prediction),
        ("Fleet Optimization", run_optimization),
        ("Analysis Pipeline", run_analysis),
        ("Feature Analysis", run_feature_analysis),
        ("Automated Reporting", run_reporting)
    ]
    
    results = {}
    successful_steps = 0
    
    for step_name, step_function in steps:
        try:
            success = step_function()
            results[step_name] = success
            if success:
                successful_steps += 1
            else:
                logger.warning(f"Step '{step_name}' failed but continuing with workflow")
        except Exception as e:
            logger.error(f"Step '{step_name}' failed with exception: {str(e)}")
            results[step_name] = False
    
    # Summary
    logger.info("=== Workflow Summary ===")
    logger.info(f"Total steps: {len(steps)}")
    logger.info(f"Successful steps: {successful_steps}")
    logger.info(f"Failed steps: {len(steps) - successful_steps}")
    
    for step_name, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logger.info(f"  {step_name}: {status}")
    
    # Final status
    if successful_steps == len(steps):
        logger.info("🎉 All workflow steps completed successfully!")
        logger.info("📁 Results are available in the 'results' directory")
        logger.info("📊 Reports are available in 'results/reports'")
        logger.info(f"🏁 End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        logger.warning(f"⚠️  Workflow completed with {len(steps) - successful_steps} failed steps")
        logger.info(f"🏁 End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return successful_steps == len(steps)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)