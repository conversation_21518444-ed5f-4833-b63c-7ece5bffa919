"""
Model Training and Results Generator
Trains all demand prediction models and saves results to models directory
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add src to path
sys.path.append('src')

from src.demand_prediction.data_preparation import prepare_dataset
from src.demand_prediction.xgboost_model import train_xgboost_model
from src.demand_prediction.lstm_model import prepare_lstm_dataset, train_lstm_model

def train_and_save_models():
    """Train all models and save results"""
    print("=== Training Demand Prediction Models ===")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Prepare dataset
        print("\n1. Preparing dataset...")
        dataset = prepare_dataset("results/processed_data")
        print(f"   ✓ Dataset prepared: {len(dataset['X_train'])} training samples")
        
        # Check target variable statistics
        print(f"   ✓ Target variable stats - Train: mean={dataset['y_train'].mean():.2f}, std={dataset['y_train'].std():.2f}")
        
        results_data = []
        
        # Train XGBoost model
        print("\n2. Training XGBoost model...")
        try:
            xgboost_model, xgboost_results = train_xgboost_model(dataset)
            
            # Save XGBoost model
            xgboost_model.save_model("models/xgboost_model_fixed.joblib")
            
            # Record results
            xgboost_metrics = xgboost_results['test_metrics'] if 'test_metrics' in xgboost_results else {
                'r2_score': xgboost_results.get('test_r2', 0),
                'rmse': xgboost_results.get('test_rmse', 0),
                'mae': xgboost_results.get('test_mae', 0),
                'mape': xgboost_results.get('test_mape', 0)
            }
            
            results_data.append({
                'model': 'XGBoost',
                'r2_score': xgboost_metrics['r2_score'],
                'rmse': xgboost_metrics['rmse'],
                'mae': xgboost_metrics['mae'],
                'mape': xgboost_metrics['mape']
            })
            
            print(f"   ✓ XGBoost trained - R²: {xgboost_metrics['r2_score']:.4f}")
            print(f"   ✓ XGBoost model saved to models/xgboost_model_fixed.joblib")
            
        except Exception as e:
            print(f"   ❌ XGBoost training failed: {str(e)}")
            results_data.append({
                'model': 'XGBoost',
                'r2_score': 0,
                'rmse': 0,
                'mae': 0,
                'mape': 0
            })
        
        # Train LSTM model
        print("\n3. Training LSTM model...")
        try:
            # Prepare LSTM dataset
            lstm_dataset = prepare_lstm_dataset(dataset, sequence_length=10)
            print(f"   ✓ LSTM sequences prepared: {len(lstm_dataset['X_train'])} sequences")
            
            # Train LSTM model
            lstm_model, lstm_results = train_lstm_model(lstm_dataset, model_type='LSTM')
            
            # Save LSTM model
            lstm_model.save_model("models/lstm_model_fixed.h5")
            
            # Record results
            lstm_metrics = lstm_results['test_metrics']
            results_data.append({
                'model': 'LSTM',
                'r2_score': lstm_metrics['r2_score'],
                'rmse': lstm_metrics['rmse'],
                'mae': lstm_metrics['mae'],
                'mape': lstm_metrics['mape']
            })
            
            print(f"   ✓ LSTM trained - R²: {lstm_metrics['r2_score']:.4f}")
            print(f"   ✓ LSTM model saved to models/lstm_model_fixed.h5")
            
        except Exception as e:
            print(f"   ❌ LSTM training failed: {str(e)}")
            results_data.append({
                'model': 'LSTM',
                'r2_score': 0,
                'rmse': 0,
                'mae': 0,
                'mape': 0
            })
        
        # Train GRU model
        print("\n4. Training GRU model...")
        try:
            # Train GRU model
            gru_model, gru_results = train_lstm_model(lstm_dataset, model_type='GRU')
            
            # Save GRU model
            gru_model.save_model("models/gru_model_fixed.h5")
            
            # Record results
            gru_metrics = gru_results['test_metrics']
            results_data.append({
                'model': 'GRU',
                'r2_score': gru_metrics['r2_score'],
                'rmse': gru_metrics['rmse'],
                'mae': gru_metrics['mae'],
                'mape': gru_metrics['mape']
            })
            
            print(f"   ✓ GRU trained - R²: {gru_metrics['r2_score']:.4f}")
            print(f"   ✓ GRU model saved to models/gru_model_fixed.h5")
            
        except Exception as e:
            print(f"   ❌ GRU training failed: {str(e)}")
            results_data.append({
                'model': 'GRU',
                'r2_score': 0,
                'rmse': 0,
                'mae': 0,
                'mape': 0
            })
        
        # Save results to CSV
        print("\n5. Saving results...")
        results_df = pd.DataFrame(results_data)
        results_df.to_csv("models/model_results_fixed.csv", index=False)
        print("   ✓ Results saved to models/model_results_fixed.csv")
        
        # Display summary
        print("\n=== Training Summary ===")
        for _, row in results_df.iterrows():
            print(f"{row['model']:8s}: R²={row['r2_score']:7.4f}, RMSE={row['rmse']:7.2f}, MAE={row['mae']:7.2f}")
        
        print(f"\n✅ All models trained and saved successfully!")
        print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return results_df
        
    except Exception as e:
        print(f"❌ Training pipeline failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    train_and_save_models()