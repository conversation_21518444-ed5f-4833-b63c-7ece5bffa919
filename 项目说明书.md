# 航空公司机型分配优化系统 - 项目说明书

## 项目概述

### 背景介绍
本项目基于A航空公司并购B公司后的实际业务需求，旨在为合并后的航空公司制定科学、高效的机型分配方案。并购完成后，A公司的每日航班数量从480次增长至815次，机队规模扩大到211架飞机，涵盖9种不同机型。面对如此复杂的运营环境，传统的人工分配方式已无法满足精细化管理的要求，迫切需要运用先进的数据科学和运筹学方法来优化资源配置。

### 项目目标
1. **收益对比分析**：深入分析A公司并购前后的财务表现变化
2. **行为模式分析**：基于机器学习技术分析旅客行程选择行为和影响销售的关键因素
3. **优化方案制定**：为815个每日航班制定最优的机型分配策略，最大化收益并降低运营成本
4. **机队效率评估**：全面分析各机型的使用情况，为未来机队调整提供数据支撑
5. **成果交付**：提供完整的课程设计报告和演示文稿

## 技术架构

### 核心技术栈
- **编程语言**：Python 3.8+
- **数据处理**：Pandas, NumPy, SciPy
- **机器学习**：Scikit-learn, XGBoost, LightGBM
- **深度学习**：TensorFlow, PyTorch
- **优化求解**：Gurobi, PuLP, OR-Tools
- **模型解释**：SHAP
- **可视化**：Matplotlib, Seaborn, Plotly

### 系统架构设计
```
数据层 → 处理层 → 模型层 → 优化层 → 分析层 → 展示层
  ↓        ↓        ↓        ↓        ↓        ↓
CSV文件 → 预处理 → ML模型 → MILP → 结果分析 → 报告生成
```

## 详细任务分解

### 第一阶段：数据预处理系统（任务1）
**复杂度评分：7/10**

#### 1.1 数据加载与验证
- 加载4个CSV数据文件：
  - `data_fam_schedule.csv`：航班时刻表（815个航班）
  - `data_fam_fleet.csv`：机队信息（211架飞机，9种机型）
  - `data_fam_products.csv`：产品销售历史（47,190种产品）
  - `data_fam_market_share.csv`：市场份额数据
- 执行数据完整性检查和基础统计分析

#### 1.2 时区转换与UTC标准化
- 处理`deptime`、`arrtime`与`depoff`、`arroff`的时区信息
- 将所有时间统一转换为UTC标准
- 精确计算航班飞行时长和飞机周转时间

#### 1.3 经停航班识别与处理
- 识别多航段经停航班（如AA0055）
- 确保经停航班的所有航段分配给同一机型
- 建立航班连续性约束关系

#### 1.4 特征工程
- 将RDx累积销售数据转换为增量销售量
- 创建时间特征：距离起飞天数、星期几、月份
- 提取产品属性：OD对、航班组合、票价等级
- 整合市场份额信息

### 第二阶段：需求预测模型（任务2）
**复杂度评分：8/10**

#### 2.1 训练验证数据准备
- 实施时间序列感知的数据分割策略
- 防止数据泄漏，确保模型泛化能力
- 特征标准化和类别变量编码
- 建立基线预测模型作为对比

#### 2.2 XGBoost模型实现
- 构建梯度提升回归模型
- 超参数调优和交叉验证
- 目标：实现R² > 0.8的预测精度
- 特征重要性分析

#### 2.3 LSTM/GRU时间序列模型
- 设计深度学习网络架构
- 处理RDx时间序列的时间依赖关系
- 与XGBoost模型性能对比
- 构建预订曲线模型

### 第三阶段：机型分配优化（任务3）
**复杂度评分：10/10（最高复杂度）**

#### 3.1 MILP模型数学表述
- **决策变量定义**：
  - X_ft：航班f分配给机型t的二元变量
  - Y_at：机场a的机型t飞机数量
  - Z_f1_f2_t：机型t的飞机轮转变量
- **目标函数**：最大化单日总利润 = 总收益 - 总运营成本

#### 3.2 核心约束条件实现
- **航班覆盖约束**：每个航班必须且仅能分配给一种机型
- **机队平衡约束**：确保每个机场的飞机流入流出平衡

#### 3.3 运营约束条件
- **40分钟最小停留时间约束**：确保飞机周转的可行性
- **经停航班连续性约束**：多航段航班使用同一机型

#### 3.4 容量与资源约束
- **座位容量约束**：旅客需求不超过机型座位数
- **机队规模约束**：使用飞机数量不超过可用数量

#### 3.5 模型求解与结果提取
- 配置Gurobi/CPLEX商业求解器
- 求解大规模混合整数线性规划问题
- 提取815个航班的最优机型分配方案
- 验证解的可行性和最优性

### 第四阶段：分析与评估（任务4-7）

#### 4. 并购前后财务分析（复杂度：3/10）
- 建立并购前A公司独立运营基线
- 构建并购后朴素分配基线
- 量化优化方案的财务价值提升

#### 5. 机队利用率分析（复杂度：4/10）
- 计算各机型的关键绩效指标：
  - 飞机利用率（每日飞行小时数）
  - 载客率（实际承载/可用座位）
  - 周转效率（平均停留时间）
  - 收益贡献和成本效益

#### 6. 敏感性分析（复杂度：6/10）
- 测试需求波动（±5%、±10%）对方案的影响
- 分析成本变化的敏感性
- 评估解决方案的鲁棒性

#### 7. SHAP特征重要性分析（复杂度：5/10）
- 使用SHAP值分析需求预测模型
- 识别影响机票销售的关键因素
- 解读旅客行程选择行为模式

### 第五阶段：报告生成（任务8）
**复杂度评分：5/10**

#### 自动化报告系统
- 整合所有数值结果和可视化图表
- 生成完整的课程设计报告
- 创建PPT演示文稿
- 确保报告的专业性和可读性

## 项目创新点

### 1. 技术融合创新
- **机器学习 + 运筹学**：将ML需求预测与MILP优化模型深度融合
- **多模型集成**：XGBoost与LSTM的组合预测策略
- **实时适应框架**：为未来引入强化学习奠定基础

### 2. 业务价值创新
- **精细化运营**：从粗放式管理转向数据驱动的精准决策
- **全局优化**：考虑815个航班和211架飞机的整体最优配置
- **动态响应**：建立对需求波动的敏感性分析体系

### 3. 方法论创新
- **多维度特征工程**：充分利用RDx时间序列数据的时间维度信息
- **约束建模**：精确建模40分钟停留时间和经停航班连续性等实际运营约束
- **可解释性增强**：通过SHAP分析提供模型决策的透明度

## 预期成果与影响

### 量化目标
- **财务指标**：并购后利润提升 > 15%，运营成本降低 > 10%
- **运营指标**：机队利用率提升 > 20%，载客率提升 > 10%
- **技术指标**：需求预测模型R² > 0.8，航班覆盖率 = 100%

### 业务价值
1. **直接经济效益**：通过优化机型分配显著提升A公司盈利能力
2. **运营效率提升**：减少机队闲置时间，提高资产利用率
3. **决策支持体系**：为未来机队调整和航线规划提供科学依据
4. **竞争优势构建**：建立基于数据科学的差异化竞争能力

### 学术贡献
1. **方法论创新**：展示机器学习与运筹学结合在实际问题中的应用
2. **案例研究价值**：为航空运输领域的学术研究提供真实数据案例
3. **技术传承**：为后续相关研究项目提供可复用的技术框架

## 风险评估与应对

### 技术风险
- **大规模优化求解复杂度**：采用商业求解器和启发式算法
- **机器学习模型精度**：多模型集成和充分的特征工程
- **数据质量问题**：严格的数据验证和异常值处理

### 实施风险
- **项目时间管理**：合理的任务分解和里程碑设置
- **技术栈复杂性**：充分的技术预研和工具选型
- **结果可重现性**：完整的代码文档和版本控制

## 项目管理

### 时间规划
- **第一阶段**：数据预处理（3-4天）
- **第二阶段**：需求预测模型（4-5天）
- **第三阶段**：优化模型构建（5-6天）
- **第四阶段**：分析与评估（3-4天）
- **第五阶段**：报告撰写（2-3天）

### 质量保证
- 使用Taskmaster进行任务管理和进度追踪
- 每个子任务完成后进行代码审查和结果验证
- 建立持续集成的测试体系
- 定期进行项目状态评估和风险控制

## 总结

本项目将为A航空公司提供一个科学、高效且具备未来适应性的机型分配策略解决方案。通过先进的数据科学技术与传统运筹学方法的深度融合，不仅能够解决当前的实际业务问题，更能为航空公司建立一套可持续的数据驱动决策体系，显著提升其经济效益和市场竞争力。

项目的成功实施将证明多学科交叉方法在解决复杂工业问题中的巨大价值，为未来在航空运输、物流优化等相关领域的研究和应用提供重要的参考和借鉴。