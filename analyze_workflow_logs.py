#!/usr/bin/env python3
"""
工作流日志分析工具
分析和可视化工作流执行日志
"""

import json
import os
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import glob

def load_latest_workflow_results():
    """加载最新的工作流执行结果"""
    try:
        with open('results/workflow_execution_results_latest.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 未找到最新的工作流执行结果")
        return None

def analyze_step_performance(workflow_results):
    """分析各步骤性能"""
    if not workflow_results or 'steps' not in workflow_results:
        return None
    
    steps_data = []
    for step_name, step_info in workflow_results['steps'].items():
        steps_data.append({
            'step': step_name,
            'status': step_info.get('status', 'unknown'),
            'duration': step_info.get('duration_seconds', 0),
            'start_time': step_info.get('start_time', ''),
            'end_time': step_info.get('end_time', '')
        })
    
    return pd.DataFrame(steps_data)

def analyze_model_performance(workflow_results):
    """分析模型性能"""
    if not workflow_results or 'steps' not in workflow_results:
        return None

    demand_prediction = workflow_results['steps'].get('demand_prediction', {})
    all_models = demand_prediction.get('all_models', {})

    if not all_models:
        return None

    models_data = []
    for model_name, model_info in all_models.items():
        # 检查模型是否成功训练
        if model_info.get('status') == 'failed':
            models_data.append({
                'model': model_name,
                'status': 'failed',
                'error': model_info.get('error', 'Unknown error'),
                'training_duration': model_info.get('training_duration', 0)
            })
        elif 'metrics' in model_info:
            metrics = model_info['metrics']
            models_data.append({
                'model': model_name,
                'status': 'success',
                'r2_score': metrics.get('r2_score', 0),
                'rmse': metrics.get('rmse', 0),
                'mae': metrics.get('mae', 0),
                'mape': metrics.get('mape', 0),
                'training_duration': model_info.get('training_duration', 0)
            })
        else:
            models_data.append({
                'model': model_name,
                'status': 'unknown',
                'training_duration': model_info.get('training_duration', 0)
            })

    return pd.DataFrame(models_data)

def generate_performance_report():
    """生成性能分析报告"""
    print("=" * 60)
    print("航空公司机型分配优化系统 - 性能分析报告")
    print("=" * 60)
    
    # 加载数据
    workflow_results = load_latest_workflow_results()
    if not workflow_results:
        return
    
    print(f"📅 执行时间: {workflow_results.get('timestamp', 'Unknown')}")
    print(f"💻 系统信息: {workflow_results.get('system_info', {}).get('platform', 'Unknown')}")
    
    # 分析步骤性能
    print("\n📊 步骤执行分析:")
    print("-" * 40)
    
    steps_df = analyze_step_performance(workflow_results)
    if steps_df is not None:
        for _, row in steps_df.iterrows():
            status_icon = "✅" if row['status'] == 'success' else "❌"
            print(f"{status_icon} {row['step']}: {row['duration']:.2f}秒 ({row['status']})")
        
        total_duration = steps_df['duration'].sum()
        success_rate = (steps_df['status'] == 'success').mean() * 100
        print(f"\n📈 总执行时间: {total_duration:.2f}秒")
        print(f"📈 成功率: {success_rate:.1f}%")
    
    # 分析模型性能
    print("\n🤖 模型性能分析:")
    print("-" * 40)
    
    models_df = analyze_model_performance(workflow_results)
    if models_df is not None:
        # 分别处理成功和失败的模型
        successful_models = models_df[models_df['status'] == 'success']
        failed_models = models_df[models_df['status'] == 'failed']

        if len(successful_models) > 0:
            # 按R²分数排序
            successful_models = successful_models.sort_values('r2_score', ascending=False)

            print("✅ 成功训练的模型:")
            for _, row in successful_models.iterrows():
                print(f"🏆 {row['model']}:")
                print(f"   R² Score: {row['r2_score']:.4f}")
                print(f"   RMSE: {row['rmse']:.2f}")
                print(f"   MAE: {row['mae']:.2f}")
                print(f"   训练时间: {row['training_duration']:.2f}秒")
                print()

            # 找出最佳模型
            best_model = successful_models.iloc[0]
            print(f"🥇 最佳模型: {best_model['model']} (R²={best_model['r2_score']:.4f})")

        if len(failed_models) > 0:
            print("\n❌ 训练失败的模型:")
            for _, row in failed_models.iterrows():
                print(f"💥 {row['model']}: {row['error']}")

        if len(successful_models) == 0:
            print("⚠️ 所有模型训练都失败了")
    
    # 分析内存使用
    print("\n💾 内存使用分析:")
    print("-" * 40)
    
    if 'performance_metrics' in workflow_results:
        perf_metrics = workflow_results['performance_metrics']
        final_memory = perf_metrics.get('final_memory_usage', {})
        print(f"最终内存使用: {final_memory.get('memory_percent', 0):.1f}%")
        print(f"内存使用量: {final_memory.get('memory_used_gb', 0):.2f}GB")
        print(f"总内存: {final_memory.get('memory_total_gb', 0):.2f}GB")
    
    # 分析优化结果
    print("\n✈️ 优化结果分析:")
    print("-" * 40)
    
    final_results = workflow_results.get('final_results', {})
    optimization_results = final_results.get('optimization_results')
    
    if optimization_results:
        print(f"解决方案状态: {optimization_results.get('solution_status', 'Unknown')}")
        print(f"目标函数值: {optimization_results.get('objective_value', 0):,.0f}")
        print(f"求解时间: {optimization_results.get('solve_time', 0):.2f}秒")
    else:
        print("⚠️ 优化结果不可用")
    
    # 生成建议
    print("\n💡 改进建议:")
    print("-" * 40)
    
    if steps_df is not None:
        failed_steps = steps_df[steps_df['status'] != 'success']['step'].tolist()
        if failed_steps:
            print(f"🔧 修复失败步骤: {', '.join(failed_steps)}")
        
        # 找出最耗时的步骤
        slowest_step = steps_df.loc[steps_df['duration'].idxmax()]
        if slowest_step['duration'] > 60:  # 超过1分钟
            print(f"⚡ 优化耗时步骤: {slowest_step['step']} ({slowest_step['duration']:.1f}秒)")
    
    if models_df is not None and len(models_df) > 1:
        # 检查模型性能差异
        successful_models = models_df[models_df['status'] == 'success']
        if len(successful_models) > 1 and 'r2_score' in successful_models.columns:
            r2_std = successful_models['r2_score'].std()
            if r2_std > 0.1:
                print("📊 考虑集成学习方法提升预测精度")

        # 检查失败的模型
        failed_models = models_df[models_df['status'] == 'failed']
        if len(failed_models) > 0:
            print("🔧 修复模型训练依赖问题:")
            for _, row in failed_models.iterrows():
                if 'xgboost' in row['error']:
                    print("   - 安装XGBoost: pip install xgboost")
                elif 'mae' in row['error']:
                    print(f"   - 修复{row['model']}模型的指标计算问题")
    
    print("\n" + "=" * 60)
    print("📋 分析完成")
    print("=" * 60)

def list_available_logs():
    """列出可用的日志文件"""
    print("可用的日志文件:")
    
    # 工作流结果文件
    result_files = glob.glob('results/workflow_execution_results_*.json')
    if result_files:
        print("\n📊 工作流执行结果:")
        for file in sorted(result_files, reverse=True):
            file_time = os.path.getmtime(file)
            print(f"  - {file} ({datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')})")
    
    # 日志文件
    log_files = glob.glob('results/logs/workflow_execution_*.log')
    if log_files:
        print("\n📝 详细日志文件:")
        for file in sorted(log_files, reverse=True)[:5]:  # 显示最新5个
            file_time = os.path.getmtime(file)
            file_size = os.path.getsize(file) / 1024  # KB
            print(f"  - {file} ({file_size:.1f}KB, {datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')})")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--list':
        list_available_logs()
    else:
        generate_performance_report()
