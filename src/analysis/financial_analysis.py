"""
Financial Analysis Module
Calculates financial metrics for pre/post-merger scenarios and optimization results
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialAnalyzer:
    """
    Analyzer for financial performance of airline operations
    """
    
    def __init__(self):
        """
        Initialize the financial analyzer
        """
        self.baseline_scenarios = {}
        self.optimized_results = {}
        self.comparison_results = {}
    
    def calculate_revenue(self, flights: pd.DataFrame, demand_forecast: pd.Series, 
                         assignments: Dict[str, str], fleet_data: pd.DataFrame) -> float:
        """
        Calculate total revenue based on flight assignments and demand
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            demand_forecast (pd.Series): Predicted demand for each flight
            assignments (Dict): Flight to aircraft type assignments
            fleet_data (pd.DataFrame): Fleet information with capacity data
            
        Returns:
            float: Total revenue
        """
        logger.info("Calculating revenue")
        
        total_revenue = 0.0
        
        # Get average fare data (simplified - in practice this would come from detailed pricing data)
        avg_fare_per_flight = {}
        if 'features' in flights.columns:
            for idx, flight in flights.iterrows():
                flight_id = flight['flight']
                # Use fare from features data or default value
                avg_fare = flight.get('fare', 200)  # Default $200 fare
                avg_fare_per_flight[flight_id] = avg_fare
        else:
            # Default fare for all flights
            for flight_id in flights['flight'].unique():
                avg_fare_per_flight[flight_id] = 200
        
        # Calculate revenue for each assigned flight
        for flight_id, aircraft_type in assignments.items():
            # Get demand for this flight
            demand = demand_forecast.get(flight_id, 0)
            
            # Get fare for this flight
            fare = avg_fare_per_flight.get(flight_id, 200)
            
            # Revenue = min(demand, capacity) * fare
            # Find aircraft capacity
            aircraft_info = fleet_data[fleet_data['aircraft_type'] == aircraft_type]
            if not aircraft_info.empty:
                capacity = aircraft_info.iloc[0]['seats']
                actual_passengers = min(demand, capacity)
                flight_revenue = actual_passengers * fare
                total_revenue += flight_revenue
            else:
                # Fallback: assume some capacity
                actual_passengers = min(demand, 150)  # Default capacity
                flight_revenue = actual_passengers * fare
                total_revenue += flight_revenue
        
        logger.info(f"Total revenue calculated: ${total_revenue:,.2f}")
        return total_revenue
    
    def calculate_operating_costs(self, flights: pd.DataFrame, assignments: Dict[str, str], 
                                fleet_data: pd.DataFrame) -> float:
        """
        Calculate total operating costs based on flight assignments
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            assignments (Dict): Flight to aircraft type assignments
            fleet_data (pd.DataFrame): Fleet information with cost data
            
        Returns:
            float: Total operating costs
        """
        logger.info("Calculating operating costs")
        
        total_costs = 0.0
        
        # Calculate costs for each assigned flight
        for flight_id, aircraft_type in assignments.items():
            # Find flight information
            flight_info = flights[flights['flight'] == flight_id]
            if flight_info.empty:
                continue
                
            flight_duration = flight_info.iloc[0].get('flight_duration', 120)  # Default 2 hours
            
            # Find aircraft cost information
            aircraft_info = fleet_data[fleet_data['aircraft_type'] == aircraft_type]
            if not aircraft_info.empty:
                hourly_cost = aircraft_info.iloc[0]['hourly_cost']
                flight_cost = (flight_duration / 60) * hourly_cost
                total_costs += flight_cost
            else:
                # Fallback: assume some cost
                hourly_cost = 4000  # Default hourly cost
                flight_cost = (flight_duration / 60) * hourly_cost
                total_costs += flight_cost
        
        logger.info(f"Total operating costs calculated: ${total_costs:,.2f}")
        return total_costs
    
    def calculate_pre_merger_baseline(self, pre_merger_flights: pd.DataFrame, 
                                    pre_merger_fleet: pd.DataFrame) -> Dict:
        """
        Calculate financial baseline for pre-merger scenario (480 flights)
        
        Args:
            pre_merger_flights (pd.DataFrame): Pre-merger flight schedule (480 flights)
            pre_merger_fleet (pd.DataFrame): Pre-merger fleet data
            
        Returns:
            Dict: Pre-merger financial baseline
        """
        logger.info("Calculating pre-merger baseline")
        
        # Simplified calculation: assume average performance
        avg_flights = 480
        avg_revenue_per_flight = 25000  # Average revenue per flight
        avg_cost_per_flight = 15000     # Average cost per flight
        
        total_revenue = avg_flights * avg_revenue_per_flight
        total_costs = avg_flights * avg_cost_per_flight
        total_profit = total_revenue - total_costs
        
        baseline = {
            'scenario': 'pre_merger',
            'total_flights': avg_flights,
            'total_revenue': total_revenue,
            'total_costs': total_costs,
            'total_profit': total_profit,
            'profit_margin': total_profit / total_revenue if total_revenue > 0 else 0
        }
        
        self.baseline_scenarios['pre_merger'] = baseline
        logger.info(f"Pre-merger baseline profit: ${total_profit:,.2f}")
        return baseline
    
    def calculate_naive_post_merger_baseline(self, post_merger_flights: pd.DataFrame, 
                                           post_merger_fleet: pd.DataFrame) -> Dict:
        """
        Calculate financial baseline for naive post-merger scenario (815 flights)
        
        Args:
            post_merger_flights (pd.DataFrame): Post-merger flight schedule (815 flights)
            post_merger_fleet (pd.DataFrame): Post-merger fleet data (211 aircraft)
            
        Returns:
            Dict: Naive post-merger financial baseline
        """
        logger.info("Calculating naive post-merger baseline")
        
        # Simplified calculation: naive assignment without optimization
        total_flights = len(post_merger_flights)
        avg_revenue_per_flight = 23000  # Slightly lower due to integration challenges
        avg_cost_per_flight = 16000     # Slightly higher due to inefficiencies
        
        total_revenue = total_flights * avg_revenue_per_flight
        total_costs = total_flights * avg_cost_per_flight
        total_profit = total_revenue - total_costs
        
        baseline = {
            'scenario': 'naive_post_merger',
            'total_flights': total_flights,
            'total_revenue': total_revenue,
            'total_costs': total_costs,
            'total_profit': total_profit,
            'profit_margin': total_profit / total_revenue if total_revenue > 0 else 0
        }
        
        self.baseline_scenarios['naive_post_merger'] = baseline
        logger.info(f"Naive post-merger baseline profit: ${total_profit:,.2f}")
        return baseline
    
    def calculate_optimized_results(self, flights: pd.DataFrame, fleet_data: pd.DataFrame,
                                  assignments: Dict[str, str], demand_forecast: pd.Series) -> Dict:
        """
        Calculate financial results for optimized fleet assignment
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            fleet_data (pd.DataFrame): Fleet information
            assignments (Dict): Optimized flight assignments
            demand_forecast (pd.Series): Demand forecast data
            
        Returns:
            Dict: Optimized financial results
        """
        logger.info("Calculating optimized results")
        
        # Calculate revenue and costs
        total_revenue = self.calculate_revenue(flights, demand_forecast, assignments, fleet_data)
        total_costs = self.calculate_operating_costs(flights, assignments, fleet_data)
        total_profit = total_revenue - total_costs
        
        results = {
            'scenario': 'optimized',
            'total_flights': len(assignments),
            'total_revenue': total_revenue,
            'total_costs': total_costs,
            'total_profit': total_profit,
            'profit_margin': total_profit / total_revenue if total_revenue > 0 else 0,
            'assignments': assignments
        }
        
        self.optimized_results = results
        logger.info(f"Optimized profit: ${total_profit:,.2f}")
        return results
    
    def compare_scenarios(self) -> Dict:
        """
        Compare all scenarios and calculate improvements
        
        Returns:
            Dict: Comparison results with percentage improvements
        """
        logger.info("Comparing scenarios")
        
        if not self.baseline_scenarios or not self.optimized_results:
            raise ValueError("Baseline scenarios and optimized results must be calculated first")
        
        comparisons = {}
        
        # Compare optimized vs naive post-merger
        naive_baseline = self.baseline_scenarios.get('naive_post_merger', {})
        optimized = self.optimized_results
        
        if naive_baseline and optimized:
            profit_improvement = optimized['total_profit'] - naive_baseline['total_profit']
            profit_improvement_pct = (profit_improvement / naive_baseline['total_profit'] * 100) if naive_baseline['total_profit'] > 0 else 0
            
            revenue_improvement = optimized['total_revenue'] - naive_baseline['total_revenue']
            revenue_improvement_pct = (revenue_improvement / naive_baseline['total_revenue'] * 100) if naive_baseline['total_revenue'] > 0 else 0
            
            cost_savings = naive_baseline['total_costs'] - optimized['total_costs']
            cost_savings_pct = (cost_savings / naive_baseline['total_costs'] * 100) if naive_baseline['total_costs'] > 0 else 0
            
            comparisons['naive_vs_optimized'] = {
                'profit_improvement': profit_improvement,
                'profit_improvement_pct': profit_improvement_pct,
                'revenue_improvement': revenue_improvement,
                'revenue_improvement_pct': revenue_improvement_pct,
                'cost_savings': cost_savings,
                'cost_savings_pct': cost_savings_pct,
                'meets_target': profit_improvement_pct > 15  # 15% target
            }
        
        # Compare optimized vs pre-merger (if available)
        pre_merger_baseline = self.baseline_scenarios.get('pre_merger', {})
        if pre_merger_baseline and optimized:
            profit_improvement = optimized['total_profit'] - pre_merger_baseline['total_profit']
            profit_improvement_pct = (profit_improvement / pre_merger_baseline['total_profit'] * 100) if pre_merger_baseline['total_profit'] > 0 else 0
            
            comparisons['pre_merger_vs_optimized'] = {
                'profit_improvement': profit_improvement,
                'profit_improvement_pct': profit_improvement_pct,
                'scale_improvement': (optimized['total_flights'] / pre_merger_baseline['total_flights']) if pre_merger_baseline['total_flights'] > 0 else 0
            }
        
        self.comparison_results = comparisons
        logger.info("Scenario comparison completed")
        return comparisons
    
    def generate_financial_summary(self) -> pd.DataFrame:
        """
        Generate comprehensive financial summary
        
        Returns:
            pd.DataFrame: Financial summary table
        """
        logger.info("Generating financial summary")
        
        summary_data = []
        
        # Add baseline scenarios
        for scenario_name, scenario_data in self.baseline_scenarios.items():
            summary_data.append({
                'Scenario': scenario_name.replace('_', ' ').title(),
                'Flights': scenario_data['total_flights'],
                'Revenue': f"${scenario_data['total_revenue']:,.0f}",
                'Costs': f"${scenario_data['total_costs']:,.0f}",
                'Profit': f"${scenario_data['total_profit']:,.0f}",
                'Margin': f"{scenario_data['profit_margin']*100:.1f}%"
            })
        
        # Add optimized results
        if self.optimized_results:
            summary_data.append({
                'Scenario': 'Optimized',
                'Flights': self.optimized_results['total_flights'],
                'Revenue': f"${self.optimized_results['total_revenue']:,.0f}",
                'Costs': f"${self.optimized_results['total_costs']:,.0f}",
                'Profit': f"${self.optimized_results['total_profit']:,.0f}",
                'Margin': f"{self.optimized_results['profit_margin']*100:.1f}%"
            })
        
        summary_df = pd.DataFrame(summary_data)
        return summary_df
    
    def print_financial_report(self):
        """
        Print comprehensive financial report
        """
        print("\n=== Financial Analysis Report ===")
        
        # Print summary table
        summary_df = self.generate_financial_summary()
        print(summary_df.to_string(index=False))
        
        # Print comparisons
        if self.comparison_results:
            print(f"\n=== Performance Improvements ===")
            for comparison_name, metrics in self.comparison_results.items():
                if comparison_name == 'naive_vs_optimized':
                    print(f"Optimized vs Naive Post-Merger:")
                    print(f"  Profit Improvement: ${metrics['profit_improvement']:,.0f} ({metrics['profit_improvement_pct']:+.1f}%)")
                    print(f"  Revenue Improvement: ${metrics['revenue_improvement']:,.0f} ({metrics['revenue_improvement_pct']:+.1f}%)")
                    print(f"  Cost Savings: ${metrics['cost_savings']:,.0f} ({metrics['cost_savings_pct']:+.1f}%)")
                    print(f"  Target Met (15% profit improvement): {'YES' if metrics['meets_target'] else 'NO'}")
                elif comparison_name == 'pre_merger_vs_optimized':
                    print(f"Optimized vs Pre-Merger:")
                    print(f"  Profit Improvement: ${metrics['profit_improvement']:,.0f} ({metrics['profit_improvement_pct']:+.1f}%)")
                    print(f"  Scale Improvement: {metrics['scale_improvement']:.1f}x more flights")

def main():
    """Test the financial analyzer"""
    logger.info("Testing financial analysis module")
    
    print("Financial Analysis Module")
    print("This module calculates financial metrics for airline fleet assignment optimization.")

if __name__ == "__main__":
    main()