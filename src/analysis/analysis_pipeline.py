"""
Main Analysis Pipeline
Orchestrates the complete financial and operational analysis workflow
"""
import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, List, Tuple, Any

from .financial_analysis import FinancialAnalyzer
from .operational_analysis import OperationalAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnalysisPipeline:
    """
    Main pipeline for financial and operational analysis of fleet assignment optimization
    """
    
    def __init__(self, data_dir: str = "results/processed_data"):
        """
        Initialize the analysis pipeline
        
        Args:
            data_dir (str): Directory containing processed data and optimization results
        """
        self.data_dir = data_dir
        self.data = {}
        self.financial_analyzer = FinancialAnalyzer()
        self.operational_analyzer = OperationalAnalyzer()
        self.analysis_results = {}
    
    def load_data(self) -> Dict:
        """
        Load processed data and optimization results
        
        Returns:
            Dict: Loaded data dictionaries
        """
        logger.info(f"Loading data from {self.data_dir}")
        
        # Load required data files
        data_files = {
            'schedule': 'schedule.csv',
            'fleet': 'fleet.csv',
            'features': 'features.csv',
            'assignments': '../results/optimization/fleet_assignments.csv'
        }
        
        for name, filename in data_files.items():
            filepath = os.path.join(self.data_dir, filename)
            if os.path.exists(filepath):
                self.data[name] = pd.read_csv(filepath)
                logger.info(f"Loaded {name}: {len(self.data[name])} rows")
            else:
                # Check in parent directory for optimization results
                parent_filepath = os.path.join(os.path.dirname(self.data_dir), 'results/optimization', os.path.basename(filename))
                if os.path.exists(parent_filepath):
                    self.data[name] = pd.read_csv(parent_filepath)
                    logger.info(f"Loaded {name}: {len(self.data[name])} rows")
                else:
                    logger.warning(f"File not found: {filepath}")
                    self.data[name] = pd.DataFrame()
        
        return self.data
    
    def prepare_demand_forecast(self) -> pd.Series:
        """
        Prepare demand forecast from features data
        
        Returns:
            pd.Series: Demand forecast for each flight
        """
        logger.info("Preparing demand forecast")
        
        if 'features' not in self.data or self.data['features'].empty:
            logger.warning("No features data available, using dummy demand forecast")
            # Create dummy demand forecast
            if 'schedule' in self.data and not self.data['schedule'].empty:
                flight_ids = self.data['schedule']['flight'].unique()
                demand_forecast = pd.Series(
                    np.random.randint(50, 200, len(flight_ids)),
                    index=flight_ids
                )
            else:
                demand_forecast = pd.Series()
        else:
            # Use features data to create demand forecast
            features_df = self.data['features']
            if 'flight' in features_df.columns and 'total_bookings' in features_df.columns:
                demand_forecast = features_df.groupby('flight')['total_bookings'].sum()
            else:
                # Fallback to using fare as proxy for demand
                if 'fare' in features_df.columns:
                    demand_forecast = features_df.groupby('flight')['fare'].mean()
                else:
                    demand_forecast = pd.Series()
        
        logger.info(f"Prepared demand forecast for {len(demand_forecast)} flights")
        return demand_forecast
    
    def load_assignments(self) -> Dict[str, str]:
        """
        Load optimized flight assignments
        
        Returns:
            Dict: Flight to aircraft type assignments
        """
        logger.info("Loading optimized flight assignments")
        
        assignments = {}
        
        if 'assignments' in self.data and not self.data['assignments'].empty:
            # Convert DataFrame to dictionary
            for idx, row in self.data['assignments'].iterrows():
                flight_id = row['flight']
                aircraft_type = row['aircraft_type']
                assignments[flight_id] = aircraft_type
        else:
            logger.warning("No assignment data found, creating dummy assignments")
            # Create dummy assignments for testing
            if 'schedule' in self.data and not self.data['schedule'].empty:
                flight_ids = self.data['schedule']['flight'].unique()
                aircraft_types = self.data['fleet']['aircraft_type'].unique() if 'fleet' in self.data and not self.data['fleet'].empty else ['A320']
                
                for i, flight_id in enumerate(flight_ids):
                    assignments[flight_id] = aircraft_types[i % len(aircraft_types)]
        
        logger.info(f"Loaded assignments for {len(assignments)} flights")
        return assignments
    
    def perform_financial_analysis(self, assignments: Dict[str, str], 
                                 demand_forecast: pd.Series) -> Dict:
        """
        Perform comprehensive financial analysis
        
        Args:
            assignments (Dict): Flight to aircraft type assignments
            demand_forecast (pd.Series): Demand forecast data
            
        Returns:
            Dict: Financial analysis results
        """
        logger.info("Performing financial analysis")
        
        # Calculate baseline scenarios
        if 'schedule' in self.data and not self.data['schedule'].empty:
            # Pre-merger baseline (480 flights)
            pre_merger_flights = self.data['schedule'].head(480)  # Simplified
            self.financial_analyzer.calculate_pre_merger_baseline(
                pre_merger_flights, self.data.get('fleet', pd.DataFrame())
            )
            
            # Naive post-merger baseline (815 flights)
            self.financial_analyzer.calculate_naive_post_merger_baseline(
                self.data['schedule'], self.data.get('fleet', pd.DataFrame())
            )
        
        # Calculate optimized results
        optimized_results = self.financial_analyzer.calculate_optimized_results(
            self.data.get('schedule', pd.DataFrame()),
            self.data.get('fleet', pd.DataFrame()),
            assignments,
            demand_forecast
        )
        
        # Compare scenarios
        comparisons = self.financial_analyzer.compare_scenarios()
        
        financial_results = {
            'baselines': self.financial_analyzer.baseline_scenarios,
            'optimized': optimized_results,
            'comparisons': comparisons
        }
        
        logger.info("Financial analysis completed")
        return financial_results
    
    def perform_operational_analysis(self, assignments: Dict[str, str], 
                                   demand_forecast: pd.Series) -> Dict:
        """
        Perform comprehensive operational analysis
        
        Args:
            assignments (Dict): Flight to aircraft type assignments
            demand_forecast (pd.Series): Demand forecast data
            
        Returns:
            Dict: Operational analysis results
        """
        logger.info("Performing operational analysis")
        
        # Calculate fleet utilization
        utilization_metrics = self.operational_analyzer.calculate_fleet_utilization(
            self.data.get('schedule', pd.DataFrame()),
            assignments,
            self.data.get('fleet', pd.DataFrame())
        )
        
        # Calculate load factors
        load_factors = self.operational_analyzer.calculate_load_factors(
            assignments,
            self.data.get('fleet', pd.DataFrame()),
            demand_forecast
        )
        
        # Calculate turnaround efficiency
        turnaround_metrics = self.operational_analyzer.calculate_turnaround_efficiency(
            self.data.get('schedule', pd.DataFrame()),
            assignments
        )
        
        # Calculate airport performance
        airport_metrics = self.operational_analyzer.calculate_airport_performance(
            self.data.get('schedule', pd.DataFrame()),
            assignments
        )
        
        # Calculate KPIs
        kpis = self.operational_analyzer.calculate_key_performance_indicators()
        
        operational_results = {
            'utilization': utilization_metrics,
            'load_factors': load_factors,
            'turnaround_efficiency': turnaround_metrics,
            'airport_performance': airport_metrics,
            'kpis': kpis
        }
        
        logger.info("Operational analysis completed")
        return operational_results
    
    def save_analysis_results(self, output_dir: str = "results/analysis"):
        """
        Save analysis results to files
        
        Args:
            output_dir (str): Directory to save results
        """
        logger.info(f"Saving analysis results to {output_dir}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save financial summary
        try:
            financial_summary = self.financial_analyzer.generate_financial_summary()
            financial_summary.to_csv(os.path.join(output_dir, "financial_summary.csv"), index=False)
        except Exception as e:
            logger.warning(f"Could not save financial summary: {e}")
        
        # Save operational efficiency report
        try:
            efficiency_report = self.operational_analyzer.generate_efficiency_report()
            efficiency_report.to_csv(os.path.join(output_dir, "efficiency_report.csv"), index=False)
        except Exception as e:
            logger.warning(f"Could not save efficiency report: {e}")
        
        # Save detailed results as JSON
        try:
            import json
            with open(os.path.join(output_dir, "analysis_results.json"), 'w') as f:
                json.dump(self.analysis_results, f, indent=2, default=str)
        except Exception as e:
            logger.warning(f"Could not save analysis results JSON: {e}")
        
        logger.info("Analysis results saved successfully")
    
    def run_pipeline(self) -> Dict:
        """
        Run the complete analysis pipeline
        
        Returns:
            Dict: Complete analysis results
        """
        logger.info("Starting analysis pipeline")
        
        try:
            # Load data
            self.load_data()
            
            # Prepare demand forecast
            demand_forecast = self.prepare_demand_forecast()
            
            # Load assignments
            assignments = self.load_assignments()
            
            # Perform financial analysis
            financial_results = self.perform_financial_analysis(assignments, demand_forecast)
            
            # Perform operational analysis
            operational_results = self.perform_operational_analysis(assignments, demand_forecast)
            
            # Combine results
            self.analysis_results = {
                'financial': financial_results,
                'operational': operational_results,
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            # Save results
            self.save_analysis_results()
            
            # Print reports
            self.financial_analyzer.print_financial_report()
            self.operational_analyzer.print_efficiency_report()
            
            logger.info("Analysis pipeline completed successfully")
            return self.analysis_results
            
        except Exception as e:
            logger.error(f"Error in analysis pipeline: {str(e)}")
            raise

def main():
    """
    Main function to run the analysis pipeline
    """
    logger.info("=== Airline Fleet Assignment Analysis Pipeline ===")
    
    # Initialize pipeline
    pipeline = AnalysisPipeline()
    
    try:
        # Run the complete pipeline
        results = pipeline.run_pipeline()
        
        # Print summary
        print(f"\n=== Analysis Pipeline Summary ===")
        print(f"Financial scenarios analyzed: {len(results['financial'].get('baselines', {}))}")
        print(f"Operational metrics calculated: {len(results['operational'].get('kpis', {}))}")
        
        # Check targets
        financial_comparisons = results['financial'].get('comparisons', {})
        operational_kpis = results['operational'].get('kpis', {})
        
        print(f"\n=== Target Achievement Summary ===")
        
        # Financial targets
        naive_vs_optimized = financial_comparisons.get('naive_vs_optimized', {})
        if naive_vs_optimized:
            profit_improvement_pct = naive_vs_optimized.get('profit_improvement_pct', 0)
            meets_financial_target = naive_vs_optimized.get('meets_target', False)
            print(f"Financial Target (15% profit improvement): {'ACHIEVED' if meets_financial_target else 'NOT ACHIEVED'}")
            print(f"  Actual improvement: {profit_improvement_pct:+.1f}%")
        
        # Operational targets
        if operational_kpis:
            meets_utilization_target = operational_kpis.get('meets_utilization_target', False)
            meets_load_factor_target = operational_kpis.get('meets_load_factor_target', False)
            meets_turnaround_target = operational_kpis.get('meets_turnaround_target', False)
            
            print(f"Utilization Target (20%+): {'ACHIEVED' if meets_utilization_target else 'NOT ACHIEVED'}")
            print(f"Load Factor Target (10%+): {'ACHIEVED' if meets_load_factor_target else 'NOT ACHIEVED'}")
            print(f"Turnaround Target (90%+ compliance): {'ACHIEVED' if meets_turnaround_target else 'NOT ACHIEVED'}")
        
        print(f"\nAnalysis pipeline completed successfully!")
        
    except Exception as e:
        logger.error(f"Error running analysis pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()