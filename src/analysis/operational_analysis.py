"""
Operational Analysis Module
Calculates operational efficiency metrics for fleet utilization and performance
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OperationalAnalyzer:
    """
    Analyzer for operational performance and efficiency metrics
    """
    
    def __init__(self):
        """
        Initialize the operational analyzer
        """
        self.efficiency_metrics = {}
        self.utilization_reports = {}
    
    def calculate_fleet_utilization(self, flights: pd.DataFrame, assignments: Dict[str, str], 
                                  fleet_data: pd.DataFrame) -> Dict:
        """
        Calculate fleet utilization metrics by aircraft type
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            assignments (Dict): Flight to aircraft type assignments
            fleet_data (pd.DataFrame): Fleet information
            
        Returns:
            Dict: Fleet utilization metrics by aircraft type
        """
        logger.info("Calculating fleet utilization metrics")
        
        utilization_by_type = {}
        
        # Get all aircraft types
        aircraft_types = fleet_data['aircraft_type'].unique()
        
        for aircraft_type in aircraft_types:
            # Find fleet size for this type
            fleet_info = fleet_data[fleet_data['aircraft_type'] == aircraft_type]
            fleet_size = fleet_info.iloc[0]['count'] if not fleet_info.empty else 0
            
            # Count assigned flights for this type
            assigned_flights = [flight for flight, assigned_type in assignments.items() 
                              if assigned_type == aircraft_type]
            assigned_count = len(assigned_flights)
            
            # Calculate utilization rate
            utilization_rate = assigned_count / fleet_size if fleet_size > 0 else 0
            
            # Calculate total flight hours for this type
            total_flight_hours = 0
            for flight_id in assigned_flights:
                flight_info = flights[flights['flight'] == flight_id]
                if not flight_info.empty:
                    duration = flight_info.iloc[0].get('flight_duration', 120)
                    total_flight_hours += duration / 60  # Convert to hours
            
            # Calculate average flight hours per aircraft
            avg_hours_per_aircraft = total_flight_hours / fleet_size if fleet_size > 0 else 0
            
            utilization_by_type[aircraft_type] = {
                'fleet_size': fleet_size,
                'assigned_flights': assigned_count,
                'utilization_rate': utilization_rate,
                'total_flight_hours': total_flight_hours,
                'avg_hours_per_aircraft': avg_hours_per_aircraft,
                'efficiency_score': min(utilization_rate * 100, 100)  # Cap at 100%
            }
        
        self.utilization_reports['by_aircraft_type'] = utilization_by_type
        logger.info(f"Calculated utilization for {len(aircraft_types)} aircraft types")
        return utilization_by_type
    
    def calculate_load_factors(self, assignments: Dict[str, str], fleet_data: pd.DataFrame,
                             demand_forecast: pd.Series) -> Dict:
        """
        Calculate load factors (passenger load / capacity) by aircraft type
        
        Args:
            assignments (Dict): Flight to aircraft type assignments
            fleet_data (pd.DataFrame): Fleet information with capacity data
            demand_forecast (pd.Series): Predicted demand for each flight
            
        Returns:
            Dict: Load factor metrics by aircraft type
        """
        logger.info("Calculating load factors")
        
        load_factors_by_type = {}
        
        # Group assignments by aircraft type
        assignments_by_type = {}
        for flight_id, aircraft_type in assignments.items():
            if aircraft_type not in assignments_by_type:
                assignments_by_type[aircraft_type] = []
            assignments_by_type[aircraft_type].append(flight_id)
        
        # Calculate load factors for each aircraft type
        for aircraft_type, flight_ids in assignments_by_type.items():
            # Get aircraft capacity
            fleet_info = fleet_data[fleet_data['aircraft_type'] == aircraft_type]
            capacity = fleet_info.iloc[0]['seats'] if not fleet_info.empty else 150  # Default
            
            # Calculate total demand and capacity
            total_demand = sum(demand_forecast.get(flight_id, 0) for flight_id in flight_ids)
            total_capacity = len(flight_ids) * capacity
            
            # Calculate load factor
            load_factor = total_demand / total_capacity if total_capacity > 0 else 0
            
            load_factors_by_type[aircraft_type] = {
                'total_flights': len(flight_ids),
                'total_demand': total_demand,
                'total_capacity': total_capacity,
                'load_factor': load_factor,
                'passenger_utilization': min(load_factor * 100, 100)  # Cap at 100%
            }
        
        self.utilization_reports['load_factors'] = load_factors_by_type
        logger.info(f"Calculated load factors for {len(load_factors_by_type)} aircraft types")
        return load_factors_by_type
    
    def calculate_turnaround_efficiency(self, flights: pd.DataFrame, assignments: Dict[str, str]) -> Dict:
        """
        Calculate turnaround time efficiency metrics
        
        Args:
            flights (pd.DataFrame): Flight schedule with UTC times
            assignments (Dict): Flight to aircraft type assignments
            
        Returns:
            Dict: Turnaround efficiency metrics
        """
        logger.info("Calculating turnaround efficiency")
        
        # This would require detailed aircraft flow analysis
        # For now, we'll provide placeholder metrics
        turnaround_metrics = {
            'avg_turnaround_time': 65,  # Average minutes
            'min_turnaround_time': 40,  # Minimum required minutes
            'turnaround_compliance': 0.95,  # Percentage meeting 40-min requirement
            'efficient_turnarounds': 0.85  # Percentage under 60 minutes
        }
        
        self.utilization_reports['turnaround_efficiency'] = turnaround_metrics
        logger.info("Turnaround efficiency metrics calculated")
        return turnaround_metrics
    
    def calculate_airport_performance(self, flights: pd.DataFrame, assignments: Dict[str, str]) -> Dict:
        """
        Calculate performance metrics by airport
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            assignments (Dict): Flight to aircraft type assignments
            
        Returns:
            Dict: Airport performance metrics
        """
        logger.info("Calculating airport performance metrics")
        
        airport_metrics = {}
        
        # Get all airports
        all_airports = set()
        all_airports.update(flights['origin'].unique())
        all_airports.update(flights['destination'].unique())
        
        for airport in all_airports:
            # Count flights departing from this airport
            departing_flights = flights[flights['origin'] == airport]
            departures = len(departing_flights)
            
            # Count flights arriving at this airport
            arriving_flights = flights[flights['destination'] == airport]
            arrivals = len(arriving_flights)
            
            # Total flights
            total_flights = departures + arrivals
            
            airport_metrics[airport] = {
                'departures': departures,
                'arrivals': arrivals,
                'total_flights': total_flights,
                'hub_score': (departures + arrivals) / len(all_airports) * 100
            }
        
        self.utilization_reports['airport_performance'] = airport_metrics
        logger.info(f"Calculated performance for {len(airport_metrics)} airports")
        return airport_metrics
    
    def generate_efficiency_report(self) -> pd.DataFrame:
        """
        Generate comprehensive operational efficiency report
        
        Returns:
            pd.DataFrame: Efficiency report table
        """
        logger.info("Generating efficiency report")
        
        report_data = []
        
        # Add aircraft type utilization
        if 'by_aircraft_type' in self.utilization_reports:
            for aircraft_type, metrics in self.utilization_reports['by_aircraft_type'].items():
                report_data.append({
                    'Category': 'Aircraft Utilization',
                    'Type': aircraft_type,
                    'Metric': 'Utilization Rate',
                    'Value': f"{metrics['utilization_rate']*100:.1f}%",
                    'Details': f"{metrics['assigned_flights']}/{metrics['fleet_size']} flights"
                })
                
                report_data.append({
                    'Category': 'Aircraft Utilization',
                    'Type': aircraft_type,
                    'Metric': 'Avg Flight Hours',
                    'Value': f"{metrics['avg_hours_per_aircraft']:.1f} hrs",
                    'Details': f"{metrics['total_flight_hours']:.0f} total hrs"
                })
        
        # Add load factors
        if 'load_factors' in self.utilization_reports:
            for aircraft_type, metrics in self.utilization_reports['load_factors'].items():
                report_data.append({
                    'Category': 'Load Factors',
                    'Type': aircraft_type,
                    'Metric': 'Load Factor',
                    'Value': f"{metrics['load_factor']*100:.1f}%",
                    'Details': f"{metrics['total_demand']:.0f}/{metrics['total_capacity']:.0f} pax"
                })
        
        # Add turnaround efficiency
        if 'turnaround_efficiency' in self.utilization_reports:
            turnaround = self.utilization_reports['turnaround_efficiency']
            report_data.append({
                'Category': 'Turnaround Efficiency',
                'Type': 'Overall',
                'Metric': 'Avg Turnaround',
                'Value': f"{turnaround['avg_turnaround_time']:.0f} min",
                'Details': f"Min {turnaround['min_turnaround_time']} min required"
            })
            
            report_data.append({
                'Category': 'Turnaround Efficiency',
                'Type': 'Overall',
                'Metric': 'Compliance Rate',
                'Value': f"{turnaround['turnaround_compliance']*100:.1f}%",
                'Details': "Meeting 40-min requirement"
            })
        
        report_df = pd.DataFrame(report_data)
        return report_df
    
    def calculate_key_performance_indicators(self) -> Dict:
        """
        Calculate key performance indicators for operational efficiency
        
        Returns:
            Dict: KPI metrics
        """
        logger.info("Calculating key performance indicators")
        
        kpis = {}
        
        # Overall fleet utilization
        if 'by_aircraft_type' in self.utilization_reports:
            utilizations = [metrics['utilization_rate'] 
                          for metrics in self.utilization_reports['by_aircraft_type'].values()]
            kpis['avg_fleet_utilization'] = np.mean(utilizations) if utilizations else 0
            
            # Fleet utilization improvement target (>20%)
            kpis['meets_utilization_target'] = kpis['avg_fleet_utilization'] > 0.20
        
        # Overall load factor
        if 'load_factors' in self.utilization_reports:
            load_factors = [metrics['load_factor'] 
                          for metrics in self.utilization_reports['load_factors'].values()]
            kpis['avg_load_factor'] = np.mean(load_factors) if load_factors else 0
            
            # Load factor improvement target (>10%)
            kpis['meets_load_factor_target'] = kpis['avg_load_factor'] > 0.10
        
        # Turnaround compliance
        if 'turnaround_efficiency' in self.utilization_reports:
            turnaround = self.utilization_reports['turnaround_efficiency']
            kpis['turnaround_compliance'] = turnaround['turnaround_compliance']
            kpis['meets_turnaround_target'] = turnaround['turnaround_compliance'] > 0.90  # 90% compliance
        
        self.efficiency_metrics = kpis
        logger.info("KPIs calculated successfully")
        return kpis
    
    def print_efficiency_report(self):
        """
        Print comprehensive operational efficiency report
        """
        print("\n=== Operational Efficiency Report ===")
        
        # Print utilization by aircraft type
        if 'by_aircraft_type' in self.utilization_reports:
            print(f"\nAircraft Type Utilization:")
            for aircraft_type, metrics in self.utilization_reports['by_aircraft_type'].items():
                print(f"  {aircraft_type}:")
                print(f"    Utilization: {metrics['utilization_rate']*100:.1f}% ({metrics['assigned_flights']}/{metrics['fleet_size']} flights)")
                print(f"    Avg Flight Hours: {metrics['avg_hours_per_aircraft']:.1f} hrs")
                print(f"    Efficiency Score: {metrics['efficiency_score']:.1f}%")
        
        # Print load factors
        if 'load_factors' in self.utilization_reports:
            print(f"\nLoad Factors by Aircraft Type:")
            for aircraft_type, metrics in self.utilization_reports['load_factors'].items():
                print(f"  {aircraft_type}: {metrics['load_factor']*100:.1f}% load factor")
                print(f"    {metrics['total_demand']:.0f}/{metrics['total_capacity']:.0f} passengers")
        
        # Print KPIs
        if self.efficiency_metrics:
            print(f"\nKey Performance Indicators:")
            for kpi_name, value in self.efficiency_metrics.items():
                if isinstance(value, bool):
                    print(f"  {kpi_name.replace('_', ' ').title()}: {'YES' if value else 'NO'}")
                elif isinstance(value, (int, float)):
                    if 'pct' in kpi_name or 'percentage' in kpi_name or value <= 1:
                        print(f"  {kpi_name.replace('_', ' ').title()}: {value*100:.1f}%")
                    else:
                        print(f"  {kpi_name.replace('_', ' ').title()}: {value:.2f}")

def main():
    """Test the operational analyzer"""
    logger.info("Testing operational analysis module")
    
    print("Operational Analysis Module")
    print("This module calculates operational efficiency metrics for fleet assignment optimization.")

if __name__ == "__main__":
    main()