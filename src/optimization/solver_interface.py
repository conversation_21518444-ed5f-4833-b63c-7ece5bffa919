"""
Solver Interface for Fleet Assignment Optimization
Handles model solving and result extraction
"""
import pulp
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FleetAssignmentSolver:
    """
    Interface for solving fleet assignment optimization problems
    """
    
    def __init__(self, model: pulp.LpProblem = None):
        """
        Initialize the solver
        
        Args:
            model (pulp.LpProblem): Optimization model to solve
        """
        self.model = model
        self.solution = {}
        self.solver_status = None
        self.solve_time = None
        
    def solve(self, time_limit: int = 3600, gap_tolerance: float = 0.01) -> Dict:
        """
        Solve the optimization model
        
        Args:
            time_limit (int): Time limit in seconds
            gap_tolerance (float): Optimality gap tolerance
            
        Returns:
            Dict: Solution results
        """
        if self.model is None:
            raise ValueError("No model to solve. Please provide a model.")
        
        logger.info(f"Starting optimization with time limit {time_limit}s and gap tolerance {gap_tolerance}")
        
        # Configure solver settings
        solver = pulp.PULP_CBC_CMD(
            timeLimit=time_limit,
            gapRel=gap_tolerance,
            msg=True
        )
        
        # Solve the model
        start_time = time.time()
        try:
            self.model.solve(solver)
            self.solve_time = time.time() - start_time
            
            # Get solver status
            self.solver_status = pulp.LpStatus[self.model.status]
            logger.info(f"Solving completed in {self.solve_time:.2f} seconds")
            logger.info(f"Solver status: {self.solver_status}")
            
            # Extract solution
            solution_results = self._extract_solution()
            
            return solution_results
            
        except Exception as e:
            logger.error(f"Error during solving: {str(e)}")
            raise
    
    def _extract_solution(self) -> Dict:
        """
        Extract solution from solved model
        
        Returns:
            Dict: Solution results including assignments and objective value
        """
        if self.model is None:
            raise ValueError("No model solved")
        
        solution = {
            'status': self.solver_status,
            'solve_time': self.solve_time,
            'objective_value': pulp.value(self.model.objective),
            'assignments': {},
            'aircraft_distribution': {},
            'utilization': {}
        }
        
        # Extract variable values
        for var in self.model.variables():
            if var.varValue is not None and abs(var.varValue) > 1e-6:  # Non-zero values
                var_name = var.name
                var_value = var.varValue
                
                # Parse variable names to extract assignments
                if var_name.startswith('X_'):  # Flight assignment variables
                    parts = var_name.split('_')
                    if len(parts) >= 3:
                        flight = parts[1]
                        aircraft_type = '_'.join(parts[2:])  # Handle aircraft types with underscores
                        
                        if var_value > 0.5:  # Binary variable is 1
                            solution['assignments'][flight] = aircraft_type
                
                elif var_name.startswith('Y_'):  # Aircraft count variables
                    parts = var_name.split('_')
                    if len(parts) >= 3:
                        airport = parts[1]
                        aircraft_type = '_'.join(parts[2:])
                        if airport not in solution['aircraft_distribution']:
                            solution['aircraft_distribution'][airport] = {}
                        solution['aircraft_distribution'][airport][aircraft_type] = var_value
        
        # Calculate utilization metrics
        if solution['assignments']:
            solution['utilization'] = self._calculate_utilization(solution['assignments'])
        
        logger.info(f"Extracted solution for {len(solution['assignments'])} flight assignments")
        return solution
    
    def _calculate_utilization(self, assignments: Dict) -> Dict:
        """
        Calculate aircraft utilization metrics
        
        Args:
            assignments (Dict): Flight to aircraft type assignments
            
        Returns:
            Dict: Utilization metrics
        """
        utilization = {}
        
        # Count assignments by aircraft type
        aircraft_counts = {}
        for flight, aircraft_type in assignments.items():
            if aircraft_type not in aircraft_counts:
                aircraft_counts[aircraft_type] = 0
            aircraft_counts[aircraft_type] += 1
        
        # Calculate utilization percentages (this would need fleet size data)
        for aircraft_type, count in aircraft_counts.items():
            utilization[aircraft_type] = {
                'assigned_flights': count,
                'utilization_rate': count / max(count, 1)  # Placeholder
            }
        
        return utilization
    
    def validate_solution(self, solution: Dict, flights: pd.DataFrame, 
                         fleet: pd.DataFrame, multi_leg_constraints: pd.DataFrame) -> Dict:
        """
        Validate the solution against all constraints
        
        Args:
            solution (Dict): Solution to validate
            flights (pd.DataFrame): Flight schedule data
            fleet (pd.DataFrame): Fleet information data
            multi_leg_constraints (pd.DataFrame): Multi-leg flight constraints
            
        Returns:
            Dict: Validation results
        """
        logger.info("Validating solution")
        
        validation_results = {
            'coverage_valid': True,
            'fleet_balance_valid': True,
            'multi_leg_valid': True,
            'capacity_valid': True,
            'fleet_size_valid': True,
            'errors': [],
            'warnings': []
        }
        
        assignments = solution.get('assignments', {})
        
        # Validate flight coverage (each flight assigned exactly once)
        flight_ids = flights['flight'].unique()
        assigned_flights = set(assignments.keys())
        unassigned_flights = set(flight_ids) - assigned_flights
        over_assigned_flights = [f for f in assigned_flights if list(assignments.keys()).count(f) > 1]
        
        if unassigned_flights:
            validation_results['coverage_valid'] = False
            validation_results['errors'].append(f"Unassigned flights: {len(unassigned_flights)}")
        
        if over_assigned_flights:
            validation_results['coverage_valid'] = False
            validation_results['errors'].append(f"Over-assigned flights: {len(over_assigned_flights)}")
        
        # Validate multi-leg constraints
        if not multi_leg_constraints.empty:
            multi_leg_violations = []
            for idx, row in multi_leg_constraints.iterrows():
                flight1, flight2 = row['flight1'], row['flight2']
                if flight1 in assignments and flight2 in assignments:
                    if assignments[flight1] != assignments[flight2]:
                        multi_leg_violations.append((flight1, flight2))
            
            if multi_leg_violations:
                validation_results['multi_leg_valid'] = False
                validation_results['errors'].append(f"Multi-leg violations: {len(multi_leg_violations)}")
        
        # Log validation results
        valid_constraints = sum([
            validation_results['coverage_valid'],
            validation_results['fleet_balance_valid'],
            validation_results['multi_leg_valid'],
            validation_results['capacity_valid'],
            validation_results['fleet_size_valid']
        ])
        
        logger.info(f"Validation: {valid_constraints}/5 constraints satisfied")
        
        if validation_results['errors']:
            logger.warning(f"Validation errors: {validation_results['errors']}")
        
        return validation_results
    
    def save_solution(self, solution: Dict, filepath: str):
        """
        Save solution to file
        
        Args:
            solution (Dict): Solution to save
            filepath (str): Path to save solution
        """
        logger.info(f"Saving solution to {filepath}")
        
        try:
            # Convert solution to DataFrame for easier saving
            assignments_df = pd.DataFrame([
                {'flight': flight, 'aircraft_type': aircraft_type}
                for flight, aircraft_type in solution.get('assignments', {}).items()
            ])
            
            assignments_df.to_csv(filepath, index=False)
            logger.info(f"Solution saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving solution: {str(e)}")
            raise
    
    def print_solution_summary(self, solution: Dict):
        """
        Print solution summary
        
        Args:
            solution (Dict): Solution to summarize
        """
        print("\n=== Fleet Assignment Solution Summary ===")
        print(f"Status: {solution.get('status', 'Unknown')}")
        print(f"Solve Time: {solution.get('solve_time', 0):.2f} seconds")
        print(f"Objective Value: {solution.get('objective_value', 0):,.2f}")
        print(f"Assigned Flights: {len(solution.get('assignments', {}))}")
        
        # Show aircraft type distribution
        aircraft_counts = {}
        for aircraft_type in solution.get('assignments', {}).values():
            aircraft_counts[aircraft_type] = aircraft_counts.get(aircraft_type, 0) + 1
        
        if aircraft_counts:
            print("\nAircraft Type Distribution:")
            for aircraft_type, count in sorted(aircraft_counts.items()):
                print(f"  {aircraft_type}: {count} flights")
        
        # Show first few assignments
        assignments = list(solution.get('assignments', {}).items())[:10]
        if assignments:
            print(f"\nFirst {len(assignments)} Flight Assignments:")
            for flight, aircraft_type in assignments:
                print(f"  {flight} -> {aircraft_type}")

def main():
    """Test the solver interface"""
    logger.info("Testing fleet assignment solver interface")
    
    print("Fleet Assignment Solver Interface Module")
    print("This module handles solving the optimization model and extracting results.")

if __name__ == "__main__":
    main()