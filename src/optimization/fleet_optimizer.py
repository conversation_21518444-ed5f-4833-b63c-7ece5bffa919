"""
Main Fleet Optimization Pipeline
Orchestrates the complete fleet assignment optimization workflow
"""
import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, List, Tuple, Any

from .model_definition import FleetAssignmentModel
from .solver_interface import FleetAssignmentSolver

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FleetOptimizationPipeline:
    """
    Main pipeline for airline fleet assignment optimization
    """
    
    def __init__(self, data_dir: str = "results/processed_data"):
        """
        Initialize the optimization pipeline
        
        Args:
            data_dir (str): Directory containing processed data
        """
        self.data_dir = data_dir
        self.data = {}
        self.model = None
        self.solver = None
        self.solution = None
        self.validation_results = None
    
    def load_data(self) -> Dict:
        """
        Load processed data for optimization
        
        Returns:
            Dict: Loaded data dictionaries
        """
        logger.info(f"Loading processed data from {self.data_dir}")
        
        # Load required data files
        data_files = {
            'schedule': 'schedule.csv',
            'fleet': 'fleet.csv',
            'features': 'features.csv',
            'multi_leg_constraints': 'multi_leg_constraints.csv'
        }
        
        for name, filename in data_files.items():
            filepath = os.path.join(self.data_dir, filename)
            if os.path.exists(filepath):
                self.data[name] = pd.read_csv(filepath)
                logger.info(f"Loaded {name}: {len(self.data[name])} rows")
            else:
                logger.warning(f"File not found: {filepath}")
                self.data[name] = pd.DataFrame()
        
        return self.data
    
    def prepare_demand_forecast(self) -> pd.Series:
        """
        Prepare demand forecast from features data
        
        Returns:
            pd.Series: Demand forecast for each flight
        """
        logger.info("Preparing demand forecast")
        
        if 'features' not in self.data or self.data['features'].empty:
            logger.warning("No features data available, using dummy demand forecast")
            # Create dummy demand forecast
            if 'schedule' in self.data and not self.data['schedule'].empty:
                flight_ids = self.data['schedule']['flight'].unique()
                demand_forecast = pd.Series(
                    np.random.randint(50, 200, len(flight_ids)),
                    index=flight_ids
                )
            else:
                demand_forecast = pd.Series()
        else:
            # Use features data to create demand forecast
            # In practice, this would come from the demand prediction models
            features_df = self.data['features']
            if 'flight' in features_df.columns and 'total_bookings' in features_df.columns:
                demand_forecast = features_df.groupby('flight')['total_bookings'].sum()
            else:
                # Fallback to using fare as proxy for demand
                if 'fare' in features_df.columns:
                    demand_forecast = features_df.groupby('flight')['fare'].mean()
                else:
                    demand_forecast = pd.Series()
        
        logger.info(f"Prepared demand forecast for {len(demand_forecast)} flights")
        return demand_forecast
    
    def build_model(self, demand_forecast: pd.Series) -> 'pulp.LpProblem':
        """
        Build the complete optimization model
        
        Args:
            demand_forecast (pd.Series): Predicted demand for each flight
            
        Returns:
            pulp.LpProblem: Built optimization model
        """
        logger.info("Building optimization model")
        
        # Validate required data
        required_data = ['schedule', 'fleet']
        for data_name in required_data:
            if data_name not in self.data or self.data[data_name].empty:
                raise ValueError(f"Required data '{data_name}' not available")
        
        # Initialize model
        model_builder = FleetAssignmentModel()
        
        # Build complete model
        self.model = model_builder.build_complete_model(
            flights=self.data['schedule'],
            fleet=self.data['fleet'],
            demand_forecast=demand_forecast,
            multi_leg_constraints=self.data.get('multi_leg_constraints', pd.DataFrame())
        )
        
        logger.info("Optimization model built successfully")
        return self.model
    
    def solve_model(self, time_limit: int = 3600, gap_tolerance: float = 0.01) -> Dict:
        """
        Solve the optimization model
        
        Args:
            time_limit (int): Time limit in seconds
            gap_tolerance (float): Optimality gap tolerance
            
        Returns:
            Dict: Solution results
        """
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")
        
        logger.info("Solving optimization model")
        
        # Initialize solver
        self.solver = FleetAssignmentSolver(self.model)
        
        # Solve model
        self.solution = self.solver.solve(
            time_limit=time_limit,
            gap_tolerance=gap_tolerance
        )
        
        logger.info("Model solved successfully")
        return self.solution
    
    def validate_solution(self) -> Dict:
        """
        Validate the solution against all constraints
        
        Returns:
            Dict: Validation results
        """
        if self.solution is None:
            raise ValueError("No solution to validate. Call solve_model() first.")
        
        logger.info("Validating solution")
        
        # Initialize solver if not already done
        if self.solver is None:
            self.solver = FleetAssignmentSolver(self.model)
        
        # Validate solution
        self.validation_results = self.solver.validate_solution(
            solution=self.solution,
            flights=self.data['schedule'],
            fleet=self.data['fleet'],
            multi_leg_constraints=self.data.get('multi_leg_constraints', pd.DataFrame())
        )
        
        logger.info("Solution validation completed")
        return self.validation_results
    
    def save_results(self, output_dir: str = "results/optimization"):
        """
        Save optimization results
        
        Args:
            output_dir (str): Directory to save results
        """
        logger.info(f"Saving optimization results to {output_dir}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save solution
        if self.solution:
            solution_file = os.path.join(output_dir, "fleet_assignments.csv")
            self.solver.save_solution(self.solution, solution_file)
        
        # Save validation results
        if self.validation_results:
            validation_file = os.path.join(output_dir, "validation_results.csv")
            pd.DataFrame([self.validation_results]).to_csv(validation_file, index=False)
        
        # Save model (optional)
        if self.model:
            model_file = os.path.join(output_dir, "model.lp")
            self.model.writeLP(model_file)
        
        logger.info("Results saved successfully")
    
    def run_pipeline(self, time_limit: int = 3600, gap_tolerance: float = 0.01) -> Dict:
        """
        Run the complete fleet optimization pipeline
        
        Args:
            time_limit (int): Time limit in seconds
            gap_tolerance (float): Optimality gap tolerance
            
        Returns:
            Dict: Complete pipeline results
        """
        logger.info("Starting fleet optimization pipeline")
        
        try:
            # Load data
            self.load_data()
            
            # Prepare demand forecast
            demand_forecast = self.prepare_demand_forecast()
            
            # Build model
            self.build_model(demand_forecast)
            
            # Solve model
            self.solve_model(time_limit, gap_tolerance)
            
            # Validate solution
            self.validate_solution()
            
            # Save results
            self.save_results()
            
            # Print summary
            if self.solution:
                self.solver.print_solution_summary(self.solution)
            
            # Prepare final results
            pipeline_results = {
                'solution': self.solution,
                'validation': self.validation_results,
                'model_stats': {
                    'variables': len(self.model.variables()) if self.model else 0,
                    'constraints': len(self.model.constraints()) if self.model else 0
                }
            }
            
            logger.info("Fleet optimization pipeline completed successfully")
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Error in optimization pipeline: {str(e)}")
            raise

def main():
    """
    Main function to run the fleet optimization pipeline
    """
    logger.info("=== Airline Fleet Assignment Optimization Pipeline ===")
    
    # Initialize pipeline
    pipeline = FleetOptimizationPipeline()
    
    try:
        # Run the complete pipeline
        results = pipeline.run_pipeline(
            time_limit=300,  # 5 minutes for testing
            gap_tolerance=0.1  # 10% gap for faster solving
        )
        
        # Print validation results
        if results['validation']:
            print(f"\nValidation Results:")
            for key, value in results['validation'].items():
                if key not in ['errors', 'warnings']:
                    print(f"  {key}: {value}")
            
            if results['validation'].get('errors'):
                print(f"Errors: {results['validation']['errors']}")
            
            if results['validation'].get('warnings'):
                print(f"Warnings: {results['validation']['warnings']}")
        
        print(f"\nModel Statistics:")
        print(f"  Variables: {results['model_stats']['variables']}")
        print(f"  Constraints: {results['model_stats']['constraints']}")
        
        print("\nOptimization pipeline completed successfully!")
        
    except Exception as e:
        logger.error(f"Error running optimization pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()