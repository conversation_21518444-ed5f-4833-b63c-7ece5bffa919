"""
MILP Model Definition for Airline Fleet Assignment
Defines the mathematical formulation and decision variables for fleet assignment optimization
"""
import pulp
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FleetAssignmentModel:
    """
    Mixed Integer Linear Programming model for airline fleet assignment optimization
    """
    
    def __init__(self):
        """
        Initialize the fleet assignment model
        """
        self.model = None
        self.decision_variables = {}
        self.parameters = {}
        self.results = {}
        
    def define_decision_variables(self, flights: pd.DataFrame, fleet: pd.DataFrame) -> Dict:
        """
        Define decision variables for the MILP model
        
        Decision Variables:
        - X_ft: Binary variable indicating if flight f is assigned to aircraft type t
        - Y_at: Integer variable representing number of aircraft type t at airport a
        - Z_f1_f2_t: Binary variable indicating if aircraft of type t goes from flight f1 to flight f2
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            fleet (pd.DataFrame): Fleet information data
            
        Returns:
            Dict: Dictionary of decision variables
        """
        logger.info("Defining decision variables")
        
        # Create the optimization model
        self.model = pulp.LpProblem("Fleet_Assignment_Optimization", pulp.LpMaximize)
        
        # Get unique flights and aircraft types
        flight_ids = flights['flight'].unique()
        aircraft_types = fleet['aircraft_type'].unique()
        airports = pd.concat([flights['origin'], flights['destination']]).unique()
        
        # Decision Variable 1: X_ft - Flight to aircraft type assignment
        X_ft = {}
        for flight in flight_ids:
            for aircraft_type in aircraft_types:
                var_name = f"X_{flight}_{aircraft_type}"
                X_ft[(flight, aircraft_type)] = pulp.LpVariable(
                    var_name, cat='Binary'
                )
        
        # Decision Variable 2: Y_at - Aircraft count at airports
        Y_at = {}
        for airport in airports:
            for aircraft_type in aircraft_types:
                var_name = f"Y_{airport}_{aircraft_type}"
                Y_at[(airport, aircraft_type)] = pulp.LpVariable(
                    var_name, lowBound=0, cat='Integer'
                )
        
        # Decision Variable 3: Z_f1_f2_t - Aircraft transitions between flights
        # This will be defined based on flight connections
        Z_f1_f2_t = {}
        # We'll populate this when we have flight connection information
        
        self.decision_variables = {
            'X_ft': X_ft,
            'Y_at': Y_at,
            'Z_f1_f2_t': Z_f1_f2_t
        }
        
        logger.info(f"Defined {len(X_ft)} X_ft variables")
        logger.info(f"Defined {len(Y_at)} Y_at variables")
        
        return self.decision_variables
    
    def define_parameters(self, flights: pd.DataFrame, fleet: pd.DataFrame, 
                         demand_forecast: pd.Series, multi_leg_constraints: pd.DataFrame) -> Dict:
        """
        Define model parameters
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            fleet (pd.DataFrame): Fleet information data
            demand_forecast (pd.Series): Predicted demand for each flight
            multi_leg_constraints (pd.DataFrame): Multi-leg flight constraints
            
        Returns:
            Dict: Dictionary of model parameters
        """
        logger.info("Defining model parameters")
        
        # Revenue parameters (based on demand forecast and fares)
        revenue = {}
        for idx, flight in flights.iterrows():
            flight_id = flight['flight']
            # Revenue = demand * average fare (simplified)
            avg_fare = 200  # Placeholder - should come from actual data
            revenue[flight_id] = demand_forecast.get(flight_id, 0) * avg_fare
        
        # Cost parameters (based on aircraft type and flight duration)
        cost = {}
        for idx, flight in flights.iterrows():
            flight_id = flight['flight']
            duration = flight.get('flight_duration', 120)  # Default 2 hours
            
            # Cost varies by aircraft type
            for idx2, aircraft in fleet.iterrows():
                aircraft_type = aircraft['aircraft_type']
                hourly_cost = aircraft['hourly_cost']
                cost[(flight_id, aircraft_type)] = (duration / 60) * hourly_cost
        
        # Capacity parameters
        capacity = {}
        for idx, aircraft in fleet.iterrows():
            aircraft_type = aircraft['aircraft_type']
            capacity[aircraft_type] = aircraft['seats']
        
        # Fleet size parameters
        fleet_size = {}
        for idx, aircraft in fleet.iterrows():
            aircraft_type = aircraft['aircraft_type']
            fleet_size[aircraft_type] = aircraft['count']
        
        self.parameters = {
            'revenue': revenue,
            'cost': cost,
            'capacity': capacity,
            'fleet_size': fleet_size,
            'multi_leg_constraints': multi_leg_constraints
        }
        
        logger.info(f"Defined parameters for {len(revenue)} flights and {len(fleet)} aircraft types")
        return self.parameters
    
    def define_objective_function(self):
        """
        Define the objective function to maximize profit
        Maximize: Σ(revenue_f - cost_f) for all flights f
        """
        logger.info("Defining objective function")
        
        if not self.model or not self.decision_variables or not self.parameters:
            raise ValueError("Model, variables, or parameters not defined")
        
        X_ft = self.decision_variables['X_ft']
        revenue = self.parameters['revenue']
        cost = self.parameters['cost']
        
        # Objective: Maximize total profit
        objective_terms = []
        
        for (flight, aircraft_type), var in X_ft.items():
            flight_revenue = revenue.get(flight, 0)
            flight_cost = cost.get((flight, aircraft_type), 0)
            profit = flight_revenue - flight_cost
            objective_terms.append(profit * var)
        
        self.model += pulp.lpSum(objective_terms)
        
        logger.info("Objective function defined")
    
    def add_flight_coverage_constraints(self):
        """
        Add flight coverage constraints
        Each flight must be assigned exactly one aircraft type
        Σ_t X_ft = 1 for all flights f
        """
        logger.info("Adding flight coverage constraints")
        
        if not self.model or not self.decision_variables:
            raise ValueError("Model or variables not defined")
        
        X_ft = self.decision_variables['X_ft']
        
        # Group variables by flight
        flights_grouped = {}
        for (flight, aircraft_type), var in X_ft.items():
            if flight not in flights_grouped:
                flights_grouped[flight] = []
            flights_grouped[flight].append(var)
        
        # Add constraint for each flight
        for flight, variables in flights_grouped.items():
            constraint_name = f"Coverage_{flight}"
            self.model += pulp.lpSum(variables) == 1, constraint_name
        
        logger.info(f"Added {len(flights_grouped)} flight coverage constraints")
    
    def add_fleet_balance_constraints(self, flights: pd.DataFrame):
        """
        Add fleet balance constraints
        Aircraft flow balance at each airport for each aircraft type
        Σ_incoming - Σ_outgoing = 0 for each airport and aircraft type
        """
        logger.info("Adding fleet balance constraints")
        
        if not self.model or not self.decision_variables:
            raise ValueError("Model or variables not defined")
        
        X_ft = self.decision_variables['X_ft']
        airports = pd.concat([flights['origin'], flights['destination']]).unique()
        aircraft_types = set()
        for (flight, aircraft_type) in X_ft.keys():
            aircraft_types.add(aircraft_type)
        
        # For each airport and aircraft type, balance incoming and outgoing flights
        balance_constraints = 0
        for airport in airports:
            for aircraft_type in aircraft_types:
                incoming_vars = []
                outgoing_vars = []
                
                # Find flights arriving at this airport with this aircraft type
                for (flight, act), var in X_ft.items():
                    if act == aircraft_type:
                        flight_info = flights[flights['flight'] == flight]
                        if not flight_info.empty:
                            if flight_info.iloc[0]['destination'] == airport:
                                incoming_vars.append(var)
                            if flight_info.iloc[0]['origin'] == airport:
                                outgoing_vars.append(var)
                
                # Add balance constraint: incoming = outgoing
                if incoming_vars or outgoing_vars:
                    constraint_name = f"Balance_{airport}_{aircraft_type}"
                    self.model += (pulp.lpSum(incoming_vars) == pulp.lpSum(outgoing_vars)), constraint_name
                    balance_constraints += 1
        
        logger.info(f"Added {balance_constraints} fleet balance constraints")
    
    def add_turnaround_time_constraints(self, flights: pd.DataFrame, min_turnaround: int = 40):
        """
        Add minimum turnaround time constraints
        Ensure minimum time between consecutive flights for the same aircraft
        
        Args:
            flights (pd.DataFrame): Flight schedule with UTC times
            min_turnaround (int): Minimum turnaround time in minutes
        """
        logger.info("Adding turnaround time constraints")
        
        # This constraint is complex and typically handled through
        # aircraft flow variables or time-indexed formulations
        # For simplicity, we'll add a note that this should be implemented
        logger.info(f"Turnaround time constraint: >= {min_turnaround} minutes between flights")
        logger.info("Note: Detailed turnaround constraints require aircraft flow modeling")
    
    def add_multi_leg_constraints(self):
        """
        Add multi-leg flight continuity constraints
        Multi-leg flights must use the same aircraft type
        """
        logger.info("Adding multi-leg flight constraints")
        
        if not self.model or not self.decision_variables or not self.parameters:
            raise ValueError("Model, variables, or parameters not defined")
        
        X_ft = self.decision_variables['X_ft']
        multi_leg_constraints = self.parameters.get('multi_leg_constraints', pd.DataFrame())
        
        if multi_leg_constraints.empty:
            logger.info("No multi-leg constraints to add")
            return
        
        constraint_count = 0
        for idx, row in multi_leg_constraints.iterrows():
            flight1 = row['flight1']
            flight2 = row['flight2']
            
            # Find all aircraft types for both flights
            aircraft_types_flight1 = [act for (f, act) in X_ft.keys() if f == flight1]
            aircraft_types_flight2 = [act for (f, act) in X_ft.keys() if f == flight2]
            
            # Add constraint that both flights use the same aircraft type
            common_aircraft_types = set(aircraft_types_flight1) & set(aircraft_types_flight2)
            
            for aircraft_type in common_aircraft_types:
                var1 = X_ft.get((flight1, aircraft_type))
                var2 = X_ft.get((flight2, aircraft_type))
                
                if var1 is not None and var2 is not None:
                    constraint_name = f"MultiLeg_{flight1}_{flight2}_{aircraft_type}"
                    self.model += var1 == var2, constraint_name
                    constraint_count += 1
        
        logger.info(f"Added {constraint_count} multi-leg flight constraints")
    
    def add_capacity_constraints(self):
        """
        Add capacity constraints
        Demand cannot exceed aircraft capacity
        demand_f * X_ft <= capacity_t * X_ft for all flights f and aircraft types t
        """
        logger.info("Adding capacity constraints")
        
        if not self.model or not self.decision_variables or not self.parameters:
            raise ValueError("Model, variables, or parameters not defined")
        
        X_ft = self.decision_variables['X_ft']
        capacity = self.parameters['capacity']
        demand = self.parameters['revenue']  # Using revenue as proxy for demand
        
        constraint_count = 0
        for (flight, aircraft_type), var in X_ft.items():
            flight_demand = demand.get(flight, 0)
            aircraft_capacity = capacity.get(aircraft_type, 100)  # Default capacity
            
            # Big-M constraint: demand <= capacity * assignment
            # This is automatically satisfied when X_ft = 0
            # When X_ft = 1, demand <= capacity
            if flight_demand > aircraft_capacity:
                constraint_name = f"Capacity_{flight}_{aircraft_type}"
                # This constraint will make the problem infeasible if demand > capacity
                # In practice, we might want to handle overbooking differently
                self.model += var == 0, constraint_name  # Force assignment to 0
                constraint_count += 1
        
        logger.info(f"Added {constraint_count} capacity constraints")
    
    def add_fleet_size_constraints(self):
        """
        Add fleet size constraints
        Cannot assign more aircraft than available
        Σ_f X_ft <= fleet_size_t for all aircraft types t
        """
        logger.info("Adding fleet size constraints")
        
        if not self.model or not self.decision_variables or not self.parameters:
            raise ValueError("Model, variables, or parameters not defined")
        
        X_ft = self.decision_variables['X_ft']
        fleet_size = self.parameters['fleet_size']
        
        constraint_count = 0
        for aircraft_type, max_count in fleet_size.items():
            # Find all assignment variables for this aircraft type
            assignment_vars = [var for (flight, act), var in X_ft.items() if act == aircraft_type]
            
            if assignment_vars:
                constraint_name = f"FleetSize_{aircraft_type}"
                self.model += pulp.lpSum(assignment_vars) <= max_count, constraint_name
                constraint_count += 1
        
        logger.info(f"Added {constraint_count} fleet size constraints")
    
    def build_complete_model(self, flights: pd.DataFrame, fleet: pd.DataFrame, 
                           demand_forecast: pd.Series, multi_leg_constraints: pd.DataFrame) -> pulp.LpProblem:
        """
        Build the complete MILP model
        
        Args:
            flights (pd.DataFrame): Flight schedule data
            fleet (pd.DataFrame): Fleet information data
            demand_forecast (pd.Series): Predicted demand for each flight
            multi_leg_constraints (pd.DataFrame): Multi-leg flight constraints
            
        Returns:
            pulp.LpProblem: Complete optimization model
        """
        logger.info("Building complete fleet assignment model")
        
        # Define decision variables
        self.define_decision_variables(flights, fleet)
        
        # Define parameters
        self.define_parameters(flights, fleet, demand_forecast, multi_leg_constraints)
        
        # Define objective function
        self.define_objective_function()
        
        # Add constraints
        self.add_flight_coverage_constraints()
        self.add_fleet_balance_constraints(flights)
        self.add_turnaround_time_constraints(flights)
        self.add_multi_leg_constraints()
        self.add_capacity_constraints()
        self.add_fleet_size_constraints()
        
        logger.info("Complete model built successfully")
        return self.model

def main():
    """Test the model definition"""
    logger.info("Testing fleet assignment model definition")
    
    # This would be called from the main optimization pipeline
    print("Fleet Assignment Model Definition Module")
    print("This module defines the MILP formulation for airline fleet assignment optimization.")

if __name__ == "__main__":
    main()