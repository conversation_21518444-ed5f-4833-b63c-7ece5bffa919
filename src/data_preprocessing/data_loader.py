"""
Data Loading and Validation Module
Handles loading of CSV files and initial data validation
"""
import pandas as pd
import numpy as np
from typing import Dict, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_flight_schedule(filepath: str) -> pd.DataFrame:
    """
    Load and validate flight schedule data
    
    Args:
        filepath (str): Path to flight schedule CSV file
        
    Returns:
        pd.DataFrame: Loaded flight schedule data
    """
    logger.info(f"Loading flight schedule data from {filepath}")
    
    # Load the data
    df = pd.read_csv(filepath)
    
    # Validate required columns
    required_columns = ['flight', 'deptime', 'depoff', 'arrtime', 'arroff', 'origin', 'destination']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Convert time columns to appropriate types
    df['deptime'] = pd.to_numeric(df['deptime'], errors='coerce')
    df['arrtime'] = pd.to_numeric(df['arrtime'], errors='coerce')
    df['depoff'] = pd.to_numeric(df['depoff'], errors='coerce')
    df['arroff'] = pd.to_numeric(df['arroff'], errors='coerce')
    
    logger.info(f"Loaded {len(df)} flight records")
    return df

def load_fleet_data(filepath: str) -> pd.DataFrame:
    """
    Load and validate fleet data
    
    Args:
        filepath (str): Path to fleet CSV file
        
    Returns:
        pd.DataFrame: Loaded fleet data
    """
    logger.info(f"Loading fleet data from {filepath}")
    
    # Load the data
    df = pd.read_csv(filepath)
    
    # Validate required columns
    required_columns = ['机型', '座位数', '飞机数量', '每小时飞行成本']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Rename columns to English for consistency
    df = df.rename(columns={
        '机型': 'aircraft_type',
        '座位数': 'seats',
        '飞机数量': 'count',
        '每小时飞行成本': 'hourly_cost'
    })
    
    # Convert to appropriate types
    df['seats'] = pd.to_numeric(df['seats'], errors='coerce')
    df['count'] = pd.to_numeric(df['count'], errors='coerce')
    df['hourly_cost'] = pd.to_numeric(df['hourly_cost'], errors='coerce')
    
    logger.info(f"Loaded {len(df)} aircraft types")
    return df

def load_product_data(filepath: str) -> pd.DataFrame:
    """
    Load and validate product sales data
    
    Args:
        filepath (str): Path to product CSV file
        
    Returns:
        pd.DataFrame: Loaded product data
    """
    logger.info(f"Loading product data from {filepath}")
    
    # Load the data
    df = pd.read_csv(filepath)
    
    # Validate required columns
    required_columns = ['Origin', 'Destination', 'Flight1', 'Class', 'Fare']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Rename columns for consistency
    df = df.rename(columns={
        'Origin': 'origin',
        'Destination': 'destination',
        'Flight1': 'flight1',
        'Class': 'class',
        'Fare': 'fare'
    })
    
    logger.info(f"Loaded {len(df)} product records")
    return df

def load_market_share_data(filepath: str) -> pd.DataFrame:
    """
    Load and validate market share data
    
    Args:
        filepath (str): Path to market share CSV file
        
    Returns:
        pd.DataFrame: Loaded market share data
    """
    logger.info(f"Loading market share data from {filepath}")
    
    # Load the data
    df = pd.read_csv(filepath)
    
    # Validate required columns
    required_columns = ['Org', 'Des', 'Host_share']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Rename columns for consistency
    df = df.rename(columns={
        'Org': 'origin',
        'Des': 'destination',
        'Host_share': 'market_share'
    })
    
    # Convert market share to numeric
    df['market_share'] = pd.to_numeric(df['market_share'], errors='coerce')
    
    logger.info(f"Loaded {len(df)} market share records")
    return df

def load_all_data(data_dir: str = "data") -> Dict[str, pd.DataFrame]:
    """
    Load all data files
    
    Args:
        data_dir (str): Directory containing data files
        
    Returns:
        Dict[str, pd.DataFrame]: Dictionary of all loaded dataframes
    """
    data = {}
    
    try:
        data['schedule'] = load_flight_schedule(f"{data_dir}/data_fam_schedule.csv")
        data['fleet'] = load_fleet_data(f"{data_dir}/data_fam_fleet.csv")
        data['products'] = load_product_data(f"{data_dir}/data_fam_products.csv")
        data['market_share'] = load_market_share_data(f"{data_dir}/data_fam_market_share.csv")
        
        logger.info("All data loaded successfully")
        return data
        
    except Exception as e:
        logger.error(f"Error loading data: {str(e)}")
        raise

if __name__ == "__main__":
    # Test the data loading
    try:
        data = load_all_data("../../data")
        for name, df in data.items():
            print(f"{name}: {df.shape}")
            print(df.head(2))
            print()
    except Exception as e:
        print(f"Error: {e}")