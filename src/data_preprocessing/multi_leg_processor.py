"""
Multi-leg Flight Processor
Handles identification and processing of multi-leg (stop-over) flights
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def identify_multi_leg_flights(schedule_df: pd.DataFrame) -> pd.DataFrame:
    """
    Identify multi-leg flights and create flight groups
    
    Args:
        schedule_df (pd.DataFrame): Flight schedule data
        
    Returns:
        pd.DataFrame: Schedule with multi-leg flight information
    """
    logger.info("Identifying multi-leg flights")
    
    # Make a copy to avoid modifying original data
    df = schedule_df.copy()
    
    # Extract flight number (remove suffixes like segment numbers)
    df['flight_base'] = df['flight'].str.extract(r'([A-Z0-9]+)')
    
    # Count occurrences of each base flight number
    flight_counts = df['flight_base'].value_counts()
    
    # Identify multi-leg flights (appearing more than once)
    multi_leg_flights = flight_counts[flight_counts > 1].index.tolist()
    
    # Add multi-leg flag
    df['is_multi_leg'] = df['flight_base'].isin(multi_leg_flights)
    
    # Create flight group IDs for multi-leg flights
    df['flight_group_id'] = df.apply(
        lambda row: row['flight_base'] if row['is_multi_leg'] else row['flight'],
        axis=1
    )
    
    # Sort multi-leg flights by departure time to establish sequence
    multi_leg_mask = df['is_multi_leg']
    df.loc[multi_leg_mask, 'dep_minutes_sorted'] = df.loc[multi_leg_mask].groupby('flight_group_id')['dep_minutes_utc'].rank(method='first')
    
    logger.info(f"Identified {len(multi_leg_flights)} multi-leg flight groups")
    return df

def validate_multi_leg_continuity(schedule_df: pd.DataFrame) -> Dict[str, List[str]]:
    """
    Validate that multi-leg flights have consistent aircraft assignments
    
    Args:
        schedule_df (pd.DataFrame): Schedule with multi-leg information
        
    Returns:
        Dict[str, List[str]]: Validation results
    """
    logger.info("Validating multi-leg flight continuity")
    
    validation_results = {
        'valid_groups': [],
        'invalid_groups': [],
        'issues': []
    }
    
    # Get multi-leg flight groups
    multi_leg_df = schedule_df[schedule_df['is_multi_leg']]
    
    if len(multi_leg_df) == 0:
        logger.info("No multi-leg flights found")
        return validation_results
    
    # Group by flight group ID
    for group_id, group in multi_leg_df.groupby('flight_group_id'):
        # Check if all segments have same origin/destination sequence
        group_sorted = group.sort_values('dep_minutes_utc')
        
        # Validate that destinations match next origins (except for last segment)
        segments = group_sorted.to_dict('records')
        valid = True
        
        for i in range(len(segments) - 1):
            if segments[i]['destination'] != segments[i + 1]['origin']:
                valid = False
                validation_results['issues'].append(
                    f"Flight group {group_id}: Segment {i+1} destination {segments[i]['destination']} "
                    f"doesn't match segment {i+2} origin {segments[i + 1]['origin']}"
                )
                break
        
        if valid:
            validation_results['valid_groups'].append(group_id)
        else:
            validation_results['invalid_groups'].append(group_id)
    
    logger.info(f"Validation complete: {len(validation_results['valid_groups'])} valid, "
                f"{len(validation_results['invalid_groups'])} invalid multi-leg groups")
    
    return validation_results

def get_multi_leg_constraints(schedule_df: pd.DataFrame) -> List[Tuple[str, str]]:
    """
    Generate constraints for multi-leg flight continuity
    
    Args:
        schedule_df (pd.DataFrame): Schedule with multi-leg information
        
    Returns:
        List[Tuple[str, str]]: List of flight pairs that must use same aircraft
    """
    constraints = []
    
    # Get multi-leg flight groups
    multi_leg_df = schedule_df[schedule_df['is_multi_leg']]
    
    if len(multi_leg_df) == 0:
        return constraints
    
    # For each multi-leg group, create continuity constraints
    for group_id, group in multi_leg_df.groupby('flight_group_id'):
        # Sort by departure time
        group_sorted = group.sort_values('dep_minutes_utc')
        flight_ids = group_sorted['flight'].tolist()
        
        # Create pairwise constraints (each consecutive pair must use same aircraft)
        for i in range(len(flight_ids) - 1):
            constraints.append((flight_ids[i], flight_ids[i + 1]))
    
    logger.info(f"Generated {len(constraints)} multi-leg continuity constraints")
    return constraints

if __name__ == "__main__":
    # Test multi-leg flight processing
    print("Multi-leg flight processing module")
    print("This module identifies and processes multi-leg flights to ensure")
    print("they are assigned to the same aircraft type throughout their journey.")