"""
Feature Engineering Module
Handles transformation of raw data into features for demand prediction
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_rdx_data(products_df: pd.DataFrame) -> pd.DataFrame:
    """
    Process RDx (booking curve) data from product sales
    
    Args:
        products_df (pd.DataFrame): Product sales data with RD columns
        
    Returns:
        pd.DataFrame: Processed data with booking curve features
    """
    logger.info("Processing RDx booking curve data")
    
    # Make a copy to avoid modifying original data
    df = products_df.copy()
    
    # Identify RD columns (RD0, RD1, RD2, etc.)
    rd_columns = [col for col in df.columns if col.startswith('RD')]
    
    if not rd_columns:
        logger.warning("No RD columns found in product data")
        return df
    
    # Convert RD columns to numeric
    for col in rd_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Calculate total bookings
    df['total_bookings'] = df[rd_columns].sum(axis=1)
    
    # Calculate booking pace features
    if len(rd_columns) > 1:
        # Early bookings (first 30% of booking window)
        early_cols = rd_columns[:max(1, len(rd_columns) // 3)]
        df['early_bookings'] = df[early_cols].sum(axis=1)
        
        # Late bookings (last 30% of booking window)
        late_cols = rd_columns[-max(1, len(rd_columns) // 3):]
        df['late_bookings'] = df[late_cols].sum(axis=1)
    
    # Calculate booking curve slope (trend)
    if len(rd_columns) > 2:
        # Simple linear trend using first and last RD values
        df['booking_trend'] = df[rd_columns[-1]] - df[rd_columns[0]]
    
    logger.info(f"Processed RDx data for {len(df)} products")
    return df

def create_time_features(schedule_df: pd.DataFrame) -> pd.DataFrame:
    """
    Create time-based features for demand prediction
    
    Args:
        schedule_df (pd.DataFrame): Flight schedule data
        
    Returns:
        pd.DataFrame: Schedule with time features
    """
    logger.info("Creating time-based features")
    
    # Make a copy to avoid modifying original data
    df = schedule_df.copy()
    
    # Extract time features from UTC departure time
    df['dep_hour'] = (df['dep_minutes_utc'] // 60).astype(int) % 24
    df['dep_minute'] = (df['dep_minutes_utc'] % 60).astype(int)
    
    # Create time of day categories
    def categorize_time_of_day(hour):
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'
    
    df['time_of_day'] = df['dep_hour'].apply(categorize_time_of_day)
    
    # Create day part features
    df['is_rush_hour'] = ((7 <= df['dep_hour']) & (df['dep_hour'] <= 9)) | \
                         ((17 <= df['dep_hour']) & (df['dep_hour'] <= 19))
    
    # Flight duration categories
    df['duration_category'] = pd.cut(df['flight_duration'], 
                                   bins=[0, 60, 180, 300, np.inf],
                                   labels=['short', 'medium', 'long', 'very_long'])
    
    logger.info(f"Created time features for {len(df)} flights")
    return df

def integrate_datasets(
    schedule_df: pd.DataFrame, 
    fleet_df: pd.DataFrame, 
    products_df: pd.DataFrame,
    market_share_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Integrate all datasets into a single feature matrix
    
    Args:
        schedule_df (pd.DataFrame): Processed flight schedule
        fleet_df (pd.DataFrame): Fleet data
        products_df (pd.DataFrame): Processed product data
        market_share_df (pd.DataFrame): Market share data
        
    Returns:
        pd.DataFrame: Integrated feature matrix
    """
    logger.info("Integrating datasets")
    
    # Create a copy of products data to avoid modifying original
    products_df_copy = products_df.copy()
    
    # Extract flight number from Flight1 column (first 6 characters) to match schedule data
    products_df_copy['flight'] = products_df_copy['flight1'].str[:6]
    
    # Merge schedule with product data on origin-destination-flight
    merged_df = schedule_df.merge(
        products_df_copy,
        left_on=['origin', 'destination', 'flight'],
        right_on=['origin', 'destination', 'flight'],
        how='left'
    )
    
    # Merge with market share data
    merged_df = merged_df.merge(
        market_share_df,
        on=['origin', 'destination'],
        how='left'
    )
    
    # Add fleet information (this will be used for capacity constraints)
    # For now, we'll add a placeholder - actual aircraft assignment will happen in optimization
    merged_df['max_capacity'] = fleet_df['seats'].max()  # Placeholder
    
    logger.info(f"Integrated datasets into {len(merged_df)} rows")
    return merged_df

def create_feature_matrix(
    schedule_df: pd.DataFrame,
    products_df: pd.DataFrame,
    market_share_df: pd.DataFrame,
    fleet_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Create complete feature matrix for demand prediction
    
    Args:
        schedule_df (pd.DataFrame): Processed flight schedule
        products_df (pd.DataFrame): Processed product data
        market_share_df (pd.DataFrame): Market share data
        fleet_df (pd.DataFrame): Fleet data
        
    Returns:
        pd.DataFrame: Complete feature matrix
    """
    logger.info("Creating complete feature matrix")
    
    # Process RDx data
    products_processed = process_rdx_data(products_df)
    
    # Create time features
    schedule_processed = create_time_features(schedule_df)
    
    # Integrate all datasets
    feature_matrix = integrate_datasets(
        schedule_processed, fleet_df, products_processed, market_share_df
    )
    
    # Aggregate bookings to flight level (sum across all classes)
    flight_level_data = feature_matrix.groupby(['origin', 'destination', 'flight']).agg({
        'dep_hour': 'first',
        'dep_minute': 'first', 
        'time_of_day': 'first',
        'is_rush_hour': 'first',
        'flight_duration': 'first',
        'duration_category': 'first',
        'market_share': 'first',
        'total_bookings': 'sum',  # Sum bookings across all classes
        'early_bookings': 'sum',
        'late_bookings': 'sum',
        'booking_trend': 'mean'
    }).reset_index()
    
    # Select relevant features for modeling
    feature_columns = [
        'origin', 'destination', 'flight',
        'dep_hour', 'dep_minute', 'time_of_day', 'is_rush_hour',
        'flight_duration', 'duration_category',
        'market_share',
        'total_bookings', 'early_bookings', 'late_bookings', 'booking_trend'
    ]
    
    # Filter to only include available columns
    available_columns = [col for col in feature_columns if col in flight_level_data.columns]
    feature_matrix_final = flight_level_data[available_columns]
    
    # Handle missing values
    numeric_columns = feature_matrix_final.select_dtypes(include=[np.number]).columns
    feature_matrix_final[numeric_columns] = feature_matrix_final[numeric_columns].fillna(0)
    
    categorical_columns = feature_matrix_final.select_dtypes(include=['object']).columns
    feature_matrix_final[categorical_columns] = feature_matrix_final[categorical_columns].fillna('unknown')
    
    logger.info(f"Created feature matrix with {len(feature_matrix_final)} rows and {len(feature_matrix_final.columns)} columns")
    return feature_matrix_final

if __name__ == "__main__":
    print("Feature engineering module for demand prediction")
    print("This module transforms raw data into features for machine learning models.")