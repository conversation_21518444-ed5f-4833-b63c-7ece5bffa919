"""
Main Data Preprocessing Pipeline
Orchestrates the complete data preprocessing workflow
"""
import pandas as pd
from typing import Di<PERSON>, <PERSON><PERSON>, List
import logging
import os

from .data_loader import load_all_data
from .timezone_converter import process_flight_times
from .multi_leg_processor import identify_multi_leg_flights, validate_multi_leg_continuity, get_multi_leg_constraints
from .feature_engineer import create_feature_matrix

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataPreprocessingPipeline:
    """
    Main pipeline for preprocessing airline data for fleet assignment optimization
    """
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the preprocessing pipeline
        
        Args:
            data_dir (str): Directory containing raw data files
        """
        self.data_dir = data_dir
        self.raw_data = None
        self.processed_data = {}
        self.multi_leg_constraints = []
        self.validation_results = {}
    
    def load_data(self) -> Dict[str, pd.DataFrame]:
        """
        Load all raw data files
        
        Returns:
            Dict[str, pd.DataFrame]: Dictionary of loaded dataframes
        """
        logger.info("Loading raw data files")
        self.raw_data = load_all_data(self.data_dir)
        return self.raw_data
    
    def process_schedule_data(self) -> pd.DataFrame:
        """
        Process flight schedule data including timezone conversion
        
        Returns:
            pd.DataFrame: Processed schedule data
        """
        if self.raw_data is None:
            raise ValueError("Raw data not loaded. Call load_data() first.")
        
        logger.info("Processing flight schedule data")
        
        # Process timezone conversions
        processed_schedule = process_flight_times(self.raw_data['schedule'])
        
        # Identify multi-leg flights
        processed_schedule = identify_multi_leg_flights(processed_schedule)
        
        # Validate multi-leg flight continuity
        self.validation_results['multi_leg'] = validate_multi_leg_continuity(processed_schedule)
        
        # Generate multi-leg constraints
        self.multi_leg_constraints = get_multi_leg_constraints(processed_schedule)
        
        self.processed_data['schedule'] = processed_schedule
        return processed_schedule
    
    def process_product_data(self) -> pd.DataFrame:
        """
        Process product sales data
        
        Returns:
            pd.DataFrame: Processed product data
        """
        if self.raw_data is None:
            raise ValueError("Raw data not loaded. Call load_data() first.")
        
        logger.info("Processing product data")
        
        # For now, we'll just store the raw product data
        # More sophisticated processing will be done in feature engineering
        self.processed_data['products'] = self.raw_data['products']
        return self.raw_data['products']
    
    def process_fleet_data(self) -> pd.DataFrame:
        """
        Process fleet data
        
        Returns:
            pd.DataFrame: Processed fleet data
        """
        if self.raw_data is None:
            raise ValueError("Raw data not loaded. Call load_data() first.")
        
        logger.info("Processing fleet data")
        
        # Fleet data is already in good format from data_loader
        self.processed_data['fleet'] = self.raw_data['fleet']
        return self.raw_data['fleet']
    
    def process_market_share_data(self) -> pd.DataFrame:
        """
        Process market share data
        
        Returns:
            pd.DataFrame: Processed market share data
        """
        if self.raw_data is None:
            raise ValueError("Raw data not loaded. Call load_data() first.")
        
        logger.info("Processing market share data")
        
        # Market share data is already in good format from data_loader
        self.processed_data['market_share'] = self.raw_data['market_share']
        return self.raw_data['market_share']
    
    def create_feature_matrix(self) -> pd.DataFrame:
        """
        Create complete feature matrix for demand prediction
        
        Returns:
            pd.DataFrame: Feature matrix for machine learning models
        """
        if not self.processed_data:
            raise ValueError("Processed data not available. Run processing steps first.")
        
        logger.info("Creating feature matrix for demand prediction")
        
        feature_matrix = create_feature_matrix(
            self.processed_data['schedule'],
            self.processed_data['products'],
            self.processed_data['market_share'],
            self.processed_data['fleet']
        )
        
        self.processed_data['features'] = feature_matrix
        return feature_matrix
    
    def run_pipeline(self) -> Dict[str, pd.DataFrame]:
        """
        Run the complete preprocessing pipeline
        
        Returns:
            Dict[str, pd.DataFrame]: All processed dataframes
        """
        logger.info("Starting data preprocessing pipeline")
        
        # Load raw data
        self.load_data()
        
        # Process each dataset
        self.process_schedule_data()
        self.process_product_data()
        self.process_fleet_data()
        self.process_market_share_data()
        
        # Create feature matrix
        self.create_feature_matrix()
        
        logger.info("Data preprocessing pipeline completed successfully")
        return self.processed_data
    
    def get_multi_leg_constraints(self) -> List[Tuple[str, str]]:
        """
        Get multi-leg flight continuity constraints
        
        Returns:
            List[Tuple[str, str]]: List of flight pairs that must use same aircraft
        """
        return self.multi_leg_constraints
    
    def get_validation_results(self) -> Dict:
        """
        Get data validation results
        
        Returns:
            Dict: Validation results for different data aspects
        """
        return self.validation_results
    
    def save_processed_data(self, output_dir: str = "results/processed_data"):
        """
        Save processed data to files
        
        Args:
            output_dir (str): Directory to save processed data
        """
        logger.info(f"Saving processed data to {output_dir}")
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Save each processed dataset
        for name, df in self.processed_data.items():
            filepath = os.path.join(output_dir, f"{name}.csv")
            df.to_csv(filepath, index=False)
            logger.info(f"Saved {name} data to {filepath}")
        
        # Save multi-leg constraints
        constraints_df = pd.DataFrame(self.multi_leg_constraints, columns=['flight1', 'flight2'])
        constraints_path = os.path.join(output_dir, "multi_leg_constraints.csv")
        constraints_df.to_csv(constraints_path, index=False)
        logger.info(f"Saved multi-leg constraints to {constraints_path}")

def main():
    """
    Main function to run the preprocessing pipeline
    """
    # Initialize pipeline
    pipeline = DataPreprocessingPipeline("data")
    
    try:
        # Run the complete pipeline
        processed_data = pipeline.run_pipeline()
        
        # Save results
        pipeline.save_processed_data("results/processed_data")
        
        # Print summary
        print("\n=== Data Preprocessing Summary ===")
        for name, df in processed_data.items():
            print(f"{name}: {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Print multi-leg flight information
        constraints = pipeline.get_multi_leg_constraints()
        print(f"\nMulti-leg flight constraints: {len(constraints)} pairs")
        
        # Print validation results
        validation_results = pipeline.get_validation_results()
        if 'multi_leg' in validation_results:
            ml_results = validation_results['multi_leg']
            print(f"Valid multi-leg groups: {len(ml_results['valid_groups'])}")
            print(f"Invalid multi-leg groups: {len(ml_results['invalid_groups'])}")
            if ml_results['issues']:
                print("Validation issues found:")
                for issue in ml_results['issues'][:5]:  # Show first 5 issues
                    print(f"  - {issue}")
        
        print("\nPreprocessing completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in preprocessing pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()