"""
Timezone Conversion Module
Handles conversion of flight times to UTC and calculation of flight durations
"""
import pandas as pd
import numpy as np
from typing import Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_to_minutes(time_str: int) -> int:
    """
    Convert time string (HHMM format) to minutes since midnight
    
    Args:
        time_str (int): Time in HHMM format
        
    Returns:
        int: Minutes since midnight
    """
    if pd.isna(time_str):
        return np.nan
    
    time_str = int(time_str)
    hours = time_str // 100
    minutes = time_str % 100
    return hours * 60 + minutes

def minutes_to_utc(minutes: int, offset: int) -> int:
    """
    Convert local time in minutes to UTC time in minutes
    
    Args:
        minutes (int): Local time in minutes since midnight
        offset (int): Timezone offset in minutes
        
    Returns:
        int: UTC time in minutes since midnight
    """
    if pd.isna(minutes) or pd.isna(offset):
        return np.nan
    
    utc_minutes = minutes - offset
    # Handle day boundary crossing
    utc_minutes = utc_minutes % (24 * 60)
    return utc_minutes

def format_minutes_as_hhmm(minutes: int) -> str:
    """
    Format minutes since midnight as HHMM string
    
    Args:
        minutes (int): Minutes since midnight
        
    Returns:
        str: Time in HHMM format
    """
    if pd.isna(minutes):
        return "NaN"
    
    hours = int(minutes // 60) % 24
    mins = int(minutes % 60)
    return f"{hours:02d}{mins:02d}"

def process_flight_times(schedule_df: pd.DataFrame) -> pd.DataFrame:
    """
    Process flight times: convert to minutes, apply timezone offsets, calculate durations
    
    Args:
        schedule_df (pd.DataFrame): Flight schedule data
        
    Returns:
        pd.DataFrame: Processed schedule with UTC times and durations
    """
    logger.info("Processing flight times and timezone conversions")
    
    # Make a copy to avoid modifying original data
    df = schedule_df.copy()
    
    # Convert local times to minutes since midnight
    df['dep_minutes_local'] = df['deptime'].apply(convert_to_minutes)
    df['arr_minutes_local'] = df['arrtime'].apply(convert_to_minutes)
    
    # Convert to UTC using timezone offsets
    df['dep_minutes_utc'] = df.apply(
        lambda row: minutes_to_utc(row['dep_minutes_local'], row['depoff']), 
        axis=1
    )
    df['arr_minutes_utc'] = df.apply(
        lambda row: minutes_to_utc(row['arr_minutes_local'], row['arroff']), 
        axis=1
    )
    
    # Calculate flight duration
    # Handle overnight flights (when arrival time < departure time)
    df['flight_duration'] = df['arr_minutes_utc'] - df['dep_minutes_utc']
    # If duration is negative, it means flight crosses midnight
    df.loc[df['flight_duration'] < 0, 'flight_duration'] += 24 * 60
    
    # Format UTC times as HHMM for display
    df['dep_time_utc'] = df['dep_minutes_utc'].apply(format_minutes_as_hhmm)
    df['arr_time_utc'] = df['arr_minutes_utc'].apply(format_minutes_as_hhmm)
    
    logger.info(f"Processed times for {len(df)} flights")
    return df

def calculate_turnaround_time(
    arrival_flight: pd.Series, 
    departure_flight: pd.Series
) -> float:
    """
    Calculate turnaround time between two flights
    
    Args:
        arrival_flight (pd.Series): Arrival flight data
        departure_flight (pd.Series): Departure flight data
        
    Returns:
        float: Turnaround time in minutes
    """
    if pd.isna(arrival_flight['arr_minutes_utc']) or pd.isna(departure_flight['dep_minutes_utc']):
        return np.nan
    
    # Calculate time difference
    turnaround = departure_flight['dep_minutes_utc'] - arrival_flight['arr_minutes_utc']
    
    # Handle overnight cases
    if turnaround < 0:
        turnaround += 24 * 60
    
    return turnaround

if __name__ == "__main__":
    # Test the timezone conversion functions
    print("Testing timezone conversion functions:")
    
    # Test time conversion
    test_time = 1430  # 14:30
    minutes = convert_to_minutes(test_time)
    print(f"{test_time} -> {minutes} minutes")
    
    # Test UTC conversion
    utc_minutes = minutes_to_utc(minutes, -400)  # EST offset
    print(f"Local {minutes} min with offset -400 -> UTC {utc_minutes} min")
    print(f"UTC time: {format_minutes_as_hhmm(utc_minutes)}")
    
    # Test overnight flight
    dep_local = convert_to_minutes(2330)  # 23:30
    arr_local = convert_to_minutes(130)   # 01:30 next day
    print(f"Overnight flight: {format_minutes_as_hhmm(dep_local)} -> {format_minutes_as_hhmm(arr_local)}")