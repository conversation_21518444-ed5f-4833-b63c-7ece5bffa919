"""
Visualization Module
Creates charts and graphs for fleet assignment analysis
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class FleetVisualization:
    """
    Class for creating visualizations of fleet assignment results
    """
    
    def __init__(self):
        """
        Initialize the visualization class
        """
        self.figures = {}
    
    def create_fleet_utilization_chart(self, utilization_data: Dict) -> plt.Figure:
        """
        Create bar chart showing fleet utilization by aircraft type
        
        Args:
            utilization_data (Dict): Fleet utilization metrics by aircraft type
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating fleet utilization chart")
        
        # Prepare data
        aircraft_types = list(utilization_data.keys())
        utilization_rates = [metrics['utilization_rate'] * 100 for metrics in utilization_data.values()]
        flight_counts = [metrics['assigned_flights'] for metrics in utilization_data.values()]
        
        # Create figure
        fig, ax1 = plt.subplots(figsize=(12, 8))
        
        # Bar chart for utilization rates
        bars = ax1.bar(aircraft_types, utilization_rates, color='skyblue', alpha=0.7)
        ax1.set_ylabel('Utilization Rate (%)', color='blue')
        ax1.set_ylim(0, 100)
        
        # Add value labels on bars
        for bar, rate in zip(bars, utilization_rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{rate:.1f}%', ha='center', va='bottom')
        
        # Secondary y-axis for flight counts
        ax2 = ax1.twinx()
        line = ax2.plot(aircraft_types, flight_counts, color='red', marker='o', linewidth=2, markersize=8)
        ax2.set_ylabel('Assigned Flights', color='red')
        
        # Labels and title
        plt.title('Fleet Utilization by Aircraft Type', fontsize=16, pad=20)
        ax1.set_xlabel('Aircraft Type')
        ax1.grid(True, alpha=0.3)
        
        # Rotate x-axis labels if needed
        plt.setp(ax1.get_xticklabels(), rotation=45, ha="right")
        
        plt.tight_layout()
        self.figures['fleet_utilization'] = fig
        return fig
    
    def create_load_factor_heatmap(self, load_factor_data: Dict) -> plt.Figure:
        """
        Create heatmap showing load factors by aircraft type
        
        Args:
            load_factor_data (Dict): Load factor metrics by aircraft type
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating load factor heatmap")
        
        # Prepare data
        aircraft_types = list(load_factor_data.keys())
        load_factors = [metrics['load_factor'] * 100 for metrics in load_factor_data.values()]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create heatmap-style bar chart
        colors = plt.cm.RdYlGn([lf/100 for lf in load_factors])
        bars = ax.bar(aircraft_types, load_factors, color=colors)
        
        # Add value labels
        for bar, lf in zip(bars, load_factors):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{lf:.1f}%', ha='center', va='bottom')
        
        # Labels and formatting
        ax.set_ylabel('Load Factor (%)')
        ax.set_xlabel('Aircraft Type')
        ax.set_title('Passenger Load Factor by Aircraft Type', fontsize=16, pad=20)
        ax.set_ylim(0, max(load_factors) * 1.2)
        ax.grid(True, alpha=0.3)
        
        # Rotate x-axis labels
        plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
        
        plt.tight_layout()
        self.figures['load_factors'] = fig
        return fig
    
    def create_financial_comparison_chart(self, financial_data: Dict) -> plt.Figure:
        """
        Create chart comparing financial performance across scenarios
        
        Args:
            financial_data (Dict): Financial results for different scenarios
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating financial comparison chart")
        
        # Prepare data
        scenarios = []
        revenues = []
        costs = []
        profits = []
        
        # Add baseline scenarios
        for scenario_name, scenario_data in financial_data.get('baselines', {}).items():
            scenarios.append(scenario_name.replace('_', ' ').title())
            revenues.append(scenario_data['total_revenue'] / 1000000)  # Convert to millions
            costs.append(scenario_data['total_costs'] / 1000000)
            profits.append(scenario_data['total_profit'] / 1000000)
        
        # Add optimized results
        if 'optimized' in financial_data:
            scenarios.append('Optimized')
            revenues.append(financial_data['optimized']['total_revenue'] / 1000000)
            costs.append(financial_data['optimized']['total_costs'] / 1000000)
            profits.append(financial_data['optimized']['total_profit'] / 1000000)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        x = np.arange(len(scenarios))
        width = 0.25
        
        # Create grouped bar chart
        bars1 = ax.bar(x - width, revenues, width, label='Revenue', color='green', alpha=0.7)
        bars2 = ax.bar(x, costs, width, label='Costs', color='red', alpha=0.7)
        bars3 = ax.bar(x + width, profits, width, label='Profit', color='blue', alpha=0.7)
        
        # Add value labels
        for bars in [bars1, bars2, bars3]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'${height:.1f}M', ha='center', va='bottom', fontsize=8)
        
        # Labels and formatting
        ax.set_xlabel('Scenario')
        ax.set_ylabel('Amount (Millions $)')
        ax.set_title('Financial Performance Comparison', fontsize=16, pad=20)
        ax.set_xticks(x)
        ax.set_xticklabels(scenarios, rotation=45, ha="right")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['financial_comparison'] = fig
        return fig
    
    def create_airport_activity_chart(self, airport_data: Dict) -> plt.Figure:
        """
        Create chart showing airport activity levels
        
        Args:
            airport_data (Dict): Airport performance metrics
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating airport activity chart")
        
        # Prepare data (top 10 busiest airports)
        sorted_airports = sorted(airport_data.items(), 
                               key=lambda x: x[1]['total_flights'], reverse=True)
        top_airports = sorted_airports[:10]
        
        airport_names = [airport[0] for airport in top_airports]
        total_flights = [airport[1]['total_flights'] for airport in top_airports]
        departures = [airport[1]['departures'] for airport in top_airports]
        arrivals = [airport[1]['arrivals'] for airport in top_airports]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        y = np.arange(len(airport_names))
        height = 0.35
        
        # Create horizontal bar chart
        bars1 = ax.barh(y - height/2, departures, height, label='Departures', color='orange', alpha=0.7)
        bars2 = ax.barh(y + height/2, arrivals, height, label='Arrivals', color='purple', alpha=0.7)
        
        # Labels and formatting
        ax.set_xlabel('Number of Flights')
        ax.set_ylabel('Airport')
        ax.set_title('Airport Activity (Top 10 Busiest)', fontsize=16, pad=20)
        ax.set_yticks(y)
        ax.set_yticklabels(airport_names)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['airport_activity'] = fig
        return fig
    
    def create_efficiency_scorecard(self, kpi_data: Dict) -> plt.Figure:
        """
        Create scorecard showing key performance indicators
        
        Args:
            kpi_data (Dict): Key performance indicators
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating efficiency scorecard")
        
        # Prepare data
        kpi_names = []
        kpi_values = []
        kpi_targets = []
        
        # Convert KPIs to displayable format
        for kpi_name, value in kpi_data.items():
            if isinstance(value, bool):
                kpi_names.append(kpi_name.replace('_', ' ').title())
                kpi_values.append(100 if value else 0)
                kpi_targets.append(100 if 'target' in kpi_name else 0)
            elif isinstance(value, (int, float)):
                if value <= 1:  # Percentage value
                    kpi_names.append(kpi_name.replace('_', ' ').title())
                    kpi_values.append(value * 100)
                    # Set targets based on project requirements
                    if 'utilization' in kpi_name:
                        kpi_targets.append(20)  # 20% target
                    elif 'load_factor' in kpi_name:
                        kpi_targets.append(10)   # 10% target
                    elif 'turnaround' in kpi_name:
                        kpi_targets.append(90)   # 90% target
                    else:
                        kpi_targets.append(0)
                else:
                    kpi_names.append(kpi_name.replace('_', ' ').title())
                    kpi_values.append(value)
                    kpi_targets.append(0)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        y_pos = np.arange(len(kpi_names))
        
        # Create bar chart
        bars = ax.barh(y_pos, kpi_values, color='lightblue', alpha=0.7, height=0.6)
        
        # Add target lines
        for i, target in enumerate(kpi_targets):
            if target > 0:
                ax.axvline(x=target, color='red', linestyle='--', alpha=0.7, 
                          ymin=i/len(kpi_names), ymax=(i+1)/len(kpi_names))
        
        # Add value labels
        for i, (bar, value, target) in enumerate(zip(bars, kpi_values, kpi_targets)):
            ax.text(value + 1, bar.get_y() + bar.get_height()/2, 
                   f'{value:.1f}%', ha='left', va='center')
            if target > 0:
                ax.text(target - 1, bar.get_y() + bar.get_height()/2,
                       f'Target: {target}%', ha='right', va='center', 
                       color='red', fontweight='bold')
        
        # Labels and formatting
        ax.set_yticks(y_pos)
        ax.set_yticklabels(kpi_names)
        ax.set_xlabel('Percentage (%)')
        ax.set_title('Key Performance Indicators', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['efficiency_scorecard'] = fig
        return fig
    
    def save_figures(self, output_dir: str = "results/reports"):
        """
        Save all generated figures to files
        
        Args:
            output_dir (str): Directory to save figures
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        for name, fig in self.figures.items():
            filepath = os.path.join(output_dir, f"{name}.png")
            fig.savefig(filepath, dpi=300, bbox_inches='tight')
            logger.info(f"Saved {name} chart to {filepath}")
    
    def show_figures(self):
        """
        Display all figures
        """
        for name, fig in self.figures.items():
            fig.show()
    
    def close_figures(self):
        """
        Close all figures to free memory
        """
        plt.close('all')
        self.figures.clear()

def main():
    """Test the visualization module"""
    logger.info("Testing visualization module")
    
    print("Visualization Module")
    print("This module creates charts and graphs for fleet assignment analysis.")

if __name__ == "__main__":
    main()