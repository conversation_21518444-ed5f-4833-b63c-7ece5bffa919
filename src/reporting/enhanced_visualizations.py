"""
Enhanced Visualization Module
Creates charts and graphs for fleet assignment analysis with model training visualization
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Any
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set style for better-looking plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class EnhancedFleetVisualization:
    """
    Enhanced class for creating visualizations of fleet assignment results and model training
    """
    
    def __init__(self):
        """
        Initialize the enhanced visualization class
        """
        self.figures = {}
        self.training_logs = {}
    
    def record_training_log(self, model_name: str, history: Dict, metrics: Dict = None):
        """
        Record training history for visualization
        
        Args:
            model_name (str): Name of the model
            history (Dict): Training history from model.fit() or similar
            metrics (Dict): Additional metrics to record
        """
        self.training_logs[model_name] = {
            'history': history,
            'metrics': metrics or {}
        }
        logger.info(f"Recorded training log for {model_name}")
    
    def create_training_progress_chart(self, model_name: str) -> plt.Figure:
        """
        Create chart showing model training progress
        
        Args:
            model_name (str): Name of the model
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        if model_name not in self.training_logs:
            logger.warning(f"No training log found for {model_name}")
            return None
            
        log_data = self.training_logs[model_name]
        history = log_data['history']
        
        # Create figure
        fig, ax1 = plt.subplots(figsize=(12, 8))
        
        # Plot training loss
        if 'loss' in history:
            epochs = range(1, len(history['loss']) + 1)
            ax1.plot(epochs, history['loss'], 'b-', label='Training Loss', linewidth=2)
            ax1.set_ylabel('Loss', color='blue')
            
            # Plot validation loss if available
            if 'val_loss' in history:
                ax1.plot(epochs, history['val_loss'], 'b--', label='Validation Loss', linewidth=2)
            
            ax1.tick_params(axis='y', labelcolor='blue')
        
        # Create second y-axis for MAE if available
        if 'mae' in history:
            ax2 = ax1.twinx()
            epochs = range(1, len(history['mae']) + 1)
            ax2.plot(epochs, history['mae'], 'r-', label='Training MAE', linewidth=2)
            
            if 'val_mae' in history:
                ax2.plot(epochs, history['val_mae'], 'r--', label='Validation MAE', linewidth=2)
                
            ax2.set_ylabel('MAE', color='red')
            ax2.tick_params(axis='y', labelcolor='red')
        
        # Labels and formatting
        plt.title(f'{model_name} Training Progress', fontsize=16, pad=20)
        ax1.set_xlabel('Epochs')
        ax1.legend(loc='upper left')
        if 'val_loss' in history or 'val_mae' in history:
            ax1.legend(loc='upper left')
            if 'val_mae' in history:
                ax2.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures[f'{model_name}_training_progress'] = fig
        return fig
    
    def create_model_comparison_chart(self, model_results: Dict) -> plt.Figure:
        """
        Create chart comparing model performance
        
        Args:
            model_results (Dict): Results from different models
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating model comparison chart")
        
        # Prepare data
        model_names = list(model_results.keys())
        r2_scores = [results.get('test_metrics', {}).get('r2_score', 0) for results in model_results.values()]
        rmse_scores = [results.get('test_metrics', {}).get('rmse', 0) for results in model_results.values()]
        
        # Create figure
        fig, ax1 = plt.subplots(figsize=(12, 8))
        
        # Bar chart for R² scores
        x = np.arange(len(model_names))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, r2_scores, width, label='R² Score', color='green', alpha=0.7)
        ax1.set_ylabel('R² Score', color='green')
        ax1.set_ylim(0, 1)
        
        # Add value labels for R²
        for bar, score in zip(bars1, r2_scores):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{score:.3f}', ha='center', va='bottom')
        
        # Secondary y-axis for RMSE
        ax2 = ax1.twinx()
        bars2 = ax2.bar(x + width/2, rmse_scores, width, label='RMSE', color='red', alpha=0.7)
        ax2.set_ylabel('RMSE', color='red')
        
        # Add value labels for RMSE
        for bar, score in zip(bars2, rmse_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{score:.1f}', ha='center', va='bottom')
        
        # Labels and formatting
        ax1.set_xlabel('Models')
        ax1.set_title('Model Performance Comparison', fontsize=16, pad=20)
        ax1.set_xticks(x)
        ax1.set_xticklabels(model_names)
        ax1.legend(loc='upper left')
        ax2.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['model_comparison'] = fig
        return fig
    
    def create_feature_importance_chart(self, feature_importance: pd.DataFrame, top_n: int = 10) -> plt.Figure:
        """
        Create chart showing feature importance
        
        Args:
            feature_importance (pd.DataFrame): Feature importance data
            top_n (int): Number of top features to show
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating feature importance chart")
        
        # Get top N features
        top_features = feature_importance.head(top_n)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Horizontal bar chart
        y_pos = np.arange(len(top_features))
        bars = ax.barh(y_pos, top_features['importance'], color='skyblue', alpha=0.7)
        
        # Add value labels
        for i, (bar, importance) in enumerate(zip(bars, top_features['importance'])):
            ax.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
                   f'{importance:.3f}', ha='left', va='center')
        
        # Labels and formatting
        ax.set_yticks(y_pos)
        ax.set_yticklabels(top_features['feature'])
        ax.set_xlabel('Importance')
        ax.set_title(f'Top {top_n} Feature Importance', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['feature_importance'] = fig
        return fig
    
    def create_prediction_scatter_plot(self, y_true: np.ndarray, y_pred: np.ndarray, model_name: str) -> plt.Figure:
        """
        Create scatter plot comparing true vs predicted values
        
        Args:
            y_true (np.ndarray): True values
            y_pred (np.ndarray): Predicted values
            model_name (str): Name of the model
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info(f"Creating prediction scatter plot for {model_name}")
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Scatter plot
        ax.scatter(y_true, y_pred, alpha=0.6, color='blue')
        
        # Perfect prediction line
        min_val = min(min(y_true), min(y_pred))
        max_val = max(max(y_true), max(y_pred))
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        
        # Calculate R²
        from sklearn.metrics import r2_score
        r2 = r2_score(y_true, y_pred)
        
        # Labels and formatting
        ax.set_xlabel('True Values')
        ax.set_ylabel('Predicted Values')
        ax.set_title(f'{model_name} - True vs Predicted Values (R² = {r2:.3f})', fontsize=16, pad=20)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures[f'{model_name}_prediction_scatter'] = fig
        return fig
    
    def create_residuals_plot(self, y_true: np.ndarray, y_pred: np.ndarray, model_name: str) -> plt.Figure:
        """
        Create residuals plot
        
        Args:
            y_true (np.ndarray): True values
            y_pred (np.ndarray): Predicted values
            model_name (str): Name of the model
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info(f"Creating residuals plot for {model_name}")
        
        # Calculate residuals
        residuals = y_true - y_pred
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Scatter plot of residuals
        ax.scatter(y_pred, residuals, alpha=0.6, color='green')
        ax.axhline(y=0, color='red', linestyle='--', linewidth=2)
        
        # Labels and formatting
        ax.set_xlabel('Predicted Values')
        ax.set_ylabel('Residuals')
        ax.set_title(f'{model_name} - Residuals Plot', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures[f'{model_name}_residuals'] = fig
        return fig
    
    def create_fleet_utilization_chart(self, utilization_data: Dict) -> plt.Figure:
        """
        Create bar chart showing fleet utilization by aircraft type
        
        Args:
            utilization_data (Dict): Fleet utilization metrics by aircraft type
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating fleet utilization chart")
        
        # Prepare data
        aircraft_types = list(utilization_data.keys())
        utilization_rates = [metrics['utilization_rate'] * 100 for metrics in utilization_data.values()]
        flight_counts = [metrics['assigned_flights'] for metrics in utilization_data.values()]
        
        # Create figure
        fig, ax1 = plt.subplots(figsize=(12, 8))
        
        # Bar chart for utilization rates
        bars = ax1.bar(aircraft_types, utilization_rates, color='skyblue', alpha=0.7)
        ax1.set_ylabel('Utilization Rate (%)', color='blue')
        ax1.set_ylim(0, 100)
        
        # Add value labels on bars
        for bar, rate in zip(bars, utilization_rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{rate:.1f}%', ha='center', va='bottom')
        
        # Secondary y-axis for flight counts
        ax2 = ax1.twinx()
        line = ax2.plot(aircraft_types, flight_counts, color='red', marker='o', linewidth=2, markersize=8)
        ax2.set_ylabel('Assigned Flights', color='red')
        
        # Labels and title
        plt.title('Fleet Utilization by Aircraft Type', fontsize=16, pad=20)
        ax1.set_xlabel('Aircraft Type')
        ax1.grid(True, alpha=0.3)
        
        # Rotate x-axis labels if needed
        plt.setp(ax1.get_xticklabels(), rotation=45, ha="right")
        
        plt.tight_layout()
        self.figures['fleet_utilization'] = fig
        return fig
    
    def create_load_factor_heatmap(self, load_factor_data: Dict) -> plt.Figure:
        """
        Create heatmap showing load factors by aircraft type
        
        Args:
            load_factor_data (Dict): Load factor metrics by aircraft type
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating load factor heatmap")
        
        # Prepare data
        aircraft_types = list(load_factor_data.keys())
        load_factors = [metrics['load_factor'] * 100 for metrics in load_factor_data.values()]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create heatmap-style bar chart
        colors = plt.cm.RdYlGn([lf/100 for lf in load_factors])
        bars = ax.bar(aircraft_types, load_factors, color=colors)
        
        # Add value labels
        for bar, lf in zip(bars, load_factors):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{lf:.1f}%', ha='center', va='bottom')
        
        # Labels and formatting
        ax.set_ylabel('Load Factor (%)')
        ax.set_xlabel('Aircraft Type')
        ax.set_title('Passenger Load Factor by Aircraft Type', fontsize=16, pad=20)
        ax.set_ylim(0, max(load_factors) * 1.2)
        ax.grid(True, alpha=0.3)
        
        # Rotate x-axis labels
        plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
        
        plt.tight_layout()
        self.figures['load_factors'] = fig
        return fig
    
    def create_financial_comparison_chart(self, financial_data: Dict) -> plt.Figure:
        """
        Create chart comparing financial performance across scenarios
        
        Args:
            financial_data (Dict): Financial results for different scenarios
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating financial comparison chart")
        
        # Prepare data
        scenarios = []
        revenues = []
        costs = []
        profits = []
        
        # Add baseline scenarios
        for scenario_name, scenario_data in financial_data.get('baselines', {}).items():
            scenarios.append(scenario_name.replace('_', ' ').title())
            revenues.append(scenario_data['total_revenue'] / 1000000)  # Convert to millions
            costs.append(scenario_data['total_costs'] / 1000000)
            profits.append(scenario_data['total_profit'] / 1000000)
        
        # Add optimized results
        if 'optimized' in financial_data:
            scenarios.append('Optimized')
            revenues.append(financial_data['optimized']['total_revenue'] / 1000000)
            costs.append(financial_data['optimized']['total_costs'] / 1000000)
            profits.append(financial_data['optimized']['total_profit'] / 1000000)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        x = np.arange(len(scenarios))
        width = 0.25
        
        # Create grouped bar chart
        bars1 = ax.bar(x - width, revenues, width, label='Revenue', color='green', alpha=0.7)
        bars2 = ax.bar(x, costs, width, label='Costs', color='red', alpha=0.7)
        bars3 = ax.bar(x + width, profits, width, label='Profit', color='blue', alpha=0.7)
        
        # Add value labels
        for bars in [bars1, bars2, bars3]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'${height:.1f}M', ha='center', va='bottom', fontsize=8)
        
        # Labels and formatting
        ax.set_xlabel('Scenario')
        ax.set_ylabel('Amount (Millions $)')
        ax.set_title('Financial Performance Comparison', fontsize=16, pad=20)
        ax.set_xticks(x)
        ax.set_xticklabels(scenarios, rotation=45, ha="right")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['financial_comparison'] = fig
        return fig
    
    def create_airport_activity_chart(self, airport_data: Dict) -> plt.Figure:
        """
        Create chart showing airport activity levels
        
        Args:
            airport_data (Dict): Airport performance metrics
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating airport activity chart")
        
        # Prepare data (top 10 busiest airports)
        sorted_airports = sorted(airport_data.items(), 
                               key=lambda x: x[1]['total_flights'], reverse=True)
        top_airports = sorted_airports[:10]
        
        airport_names = [airport[0] for airport in top_airports]
        total_flights = [airport[1]['total_flights'] for airport in top_airports]
        departures = [airport[1]['departures'] for airport in top_airports]
        arrivals = [airport[1]['arrivals'] for airport in top_airports]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        y = np.arange(len(airport_names))
        height = 0.35
        
        # Create horizontal bar chart
        bars1 = ax.barh(y - height/2, departures, height, label='Departures', color='orange', alpha=0.7)
        bars2 = ax.barh(y + height/2, arrivals, height, label='Arrivals', color='purple', alpha=0.7)
        
        # Labels and formatting
        ax.set_xlabel('Number of Flights')
        ax.set_ylabel('Airport')
        ax.set_title('Airport Activity (Top 10 Busiest)', fontsize=16, pad=20)
        ax.set_yticks(y)
        ax.set_yticklabels(airport_names)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['airport_activity'] = fig
        return fig
    
    def create_efficiency_scorecard(self, kpi_data: Dict) -> plt.Figure:
        """
        Create scorecard showing key performance indicators
        
        Args:
            kpi_data (Dict): Key performance indicators
            
        Returns:
            plt.Figure: Matplotlib figure object
        """
        logger.info("Creating efficiency scorecard")
        
        # Prepare data
        kpi_names = []
        kpi_values = []
        kpi_targets = []
        
        # Convert KPIs to displayable format
        for kpi_name, value in kpi_data.items():
            if isinstance(value, bool):
                kpi_names.append(kpi_name.replace('_', ' ').title())
                kpi_values.append(100 if value else 0)
                kpi_targets.append(100 if 'target' in kpi_name else 0)
            elif isinstance(value, (int, float)):
                if value <= 1:  # Percentage value
                    kpi_names.append(kpi_name.replace('_', ' ').title())
                    kpi_values.append(value * 100)
                    # Set targets based on project requirements
                    if 'utilization' in kpi_name:
                        kpi_targets.append(20)  # 20% target
                    elif 'load_factor' in kpi_name:
                        kpi_targets.append(10)   # 10% target
                    elif 'turnaround' in kpi_name:
                        kpi_targets.append(90)   # 90% target
                    else:
                        kpi_targets.append(0)
                else:
                    kpi_names.append(kpi_name.replace('_', ' ').title())
                    kpi_values.append(value)
                    kpi_targets.append(0)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        y_pos = np.arange(len(kpi_names))
        
        # Create bar chart
        bars = ax.barh(y_pos, kpi_values, color='lightblue', alpha=0.7, height=0.6)
        
        # Add target lines
        for i, target in enumerate(kpi_targets):
            if target > 0:
                ax.axvline(x=target, color='red', linestyle='--', alpha=0.7, 
                          ymin=i/len(kpi_names), ymax=(i+1)/len(kpi_names))
        
        # Add value labels
        for i, (bar, value, target) in enumerate(zip(bars, kpi_values, kpi_targets)):
            ax.text(value + 1, bar.get_y() + bar.get_height()/2, 
                   f'{value:.1f}%', ha='left', va='center')
            if target > 0:
                ax.text(target - 1, bar.get_y() + bar.get_height()/2,
                       f'Target: {target}%', ha='right', va='center', 
                       color='red', fontweight='bold')
        
        # Labels and formatting
        ax.set_yticks(y_pos)
        ax.set_yticklabels(kpi_names)
        ax.set_xlabel('Percentage (%)')
        ax.set_title('Key Performance Indicators', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.figures['efficiency_scorecard'] = fig
        return fig
    
    def save_figures(self, output_dir: str = "results/reports/figures"):
        """
        Save all generated figures to files
        
        Args:
            output_dir (str): Directory to save figures
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        for name, fig in self.figures.items():
            filepath = os.path.join(output_dir, f"{name}.png")
            fig.savefig(filepath, dpi=300, bbox_inches='tight')
            logger.info(f"Saved {name} chart to {filepath}")
    
    def save_training_logs(self, output_dir: str = "results/model_logs"):
        """
        Save training logs to JSON files
        
        Args:
            output_dir (str): Directory to save logs
        """
        import os
        import json
        os.makedirs(output_dir, exist_ok=True)
        
        for model_name, log_data in self.training_logs.items():
            # Convert numpy arrays to lists for JSON serialization
            serializable_log = {}
            for key, value in log_data['history'].items():
                if isinstance(value, (np.ndarray, list)):
                    serializable_log[key] = list(value)
                else:
                    serializable_log[key] = value
            
            filepath = os.path.join(output_dir, f"{model_name}_training_log.json")
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump({
                    'model_name': model_name,
                    'history': serializable_log,
                    'metrics': log_data['metrics']
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved {model_name} training log to {filepath}")
    
    def show_figures(self):
        """
        Display all figures
        """
        for name, fig in self.figures.items():
            fig.show()
    
    def close_figures(self):
        """
        Close all figures to free memory
        """
        plt.close('all')
        self.figures.clear()

def main():
    """Test the enhanced visualization module"""
    logger.info("Testing enhanced visualization module")
    
    print("Enhanced Visualization Module")
    print("This module creates charts and graphs for fleet assignment analysis with enhanced features.")

if __name__ == "__main__":
    main()