"""
SHAP Feature Importance Analysis
Analyzes trained demand prediction models using SHAP values
"""
import pandas as pd
import numpy as np
import shap
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from typing import Dict, List, Tuple, Any, Optional
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SHAPFeatureAnalyzer:
    """
    Analyzer for SHAP feature importance in demand prediction models
    """
    
    def __init__(self):
        """
        Initialize the SHAP analyzer
        """
        self.explainers = {}
        self.shap_values = {}
        self.feature_importance = {}
        self.model = None
        self.feature_names = None
    
    def load_model_and_data(self, model_path: str, data_path: str, model_type: str = 'xgboost'):
        """
        Load trained model and data for SHAP analysis
        
        Args:
            model_path (str): Path to trained model
            data_path (str): Path to training data
            model_type (str): Type of model ('xgboost', 'lstm', 'gru')
        """
        logger.info(f"Loading {model_type} model and data for SHAP analysis")
        
        try:
            if model_type == 'xgboost':
                import joblib
                self.model = joblib.load(model_path)
                logger.info("XGBoost model loaded successfully")
            elif model_type in ['lstm', 'gru']:
                import tensorflow as tf
                self.model = tf.keras.models.load_model(model_path)
                logger.info(f"{model_type.upper()} model loaded successfully")
            
            # Load data
            if data_path.endswith('.csv'):
                data_df = pd.read_csv(data_path)
                logger.info(f"Data loaded: {len(data_df)} samples")
            elif data_path.endswith('.parquet'):
                data_df = pd.read_parquet(data_path)
                logger.info(f"Data loaded: {len(data_df)} samples")
            else:
                # Assume it's a directory with multiple files
                data_files = [f for f in os.listdir(data_path) if f.endswith('.csv')]
                if data_files:
                    data_df = pd.read_csv(os.path.join(data_path, data_files[0]))
                    logger.info(f"Data loaded: {len(data_df)} samples")
                else:
                    raise ValueError(f"No data files found in {data_path}")
            
            self.data = data_df
            self.feature_names = data_df.columns.tolist()
            
        except Exception as e:
            logger.error(f"Error loading model and data: {str(e)}")
            raise
    
    def create_shap_explainer(self, model_type: str = 'xgboost', background_data: Optional[pd.DataFrame] = None):
        """
        Create SHAP explainer for the model
        
        Args:
            model_type (str): Type of model ('xgboost', 'lstm', 'gru')
            background_data (pd.DataFrame): Background data for explainer (optional)
        """
        logger.info(f"Creating SHAP explainer for {model_type}")
        
        try:
            if model_type == 'xgboost':
                # For tree models, use TreeExplainer
                self.explainer = shap.TreeExplainer(self.model)
                logger.info("TreeExplainer created successfully")
                
            elif model_type in ['lstm', 'gru']:
                # For deep learning models, use DeepExplainer or GradientExplainer
                # Use a sample of background data
                if background_data is not None:
                    background_sample = background_data.sample(min(100, len(background_data)))
                else:
                    background_sample = self.data.sample(min(100, len(self.data)))
                
                self.explainer = shap.DeepExplainer(self.model, background_sample.values)
                logger.info("DeepExplainer created successfully")
                
            else:
                # For other models, use KernelExplainer (slower but works with any model)
                def model_predict(data_array):
                    if hasattr(self.model, 'predict'):
                        return self.model.predict(data_array)
                    elif hasattr(self.model, '__call__'):
                        return self.model(data_array)
                    else:
                        raise ValueError("Model doesn't have predict method")
                
                # Use a sample of background data
                if background_data is not None:
                    background_sample = background_data.sample(min(100, len(background_data)))
                else:
                    background_sample = self.data.sample(min(100, len(self.data)))
                
                self.explainer = shap.KernelExplainer(model_predict, background_sample)
                logger.info("KernelExplainer created successfully")
                
        except Exception as e:
            logger.error(f"Error creating SHAP explainer: {str(e)}")
            raise
    
    def calculate_shap_values(self, data_sample: Optional[pd.DataFrame] = None, sample_size: int = 1000):
        """
        Calculate SHAP values for the data
        
        Args:
            data_sample (pd.DataFrame): Specific data sample to analyze (optional)
            sample_size (int): Number of samples to analyze if no specific sample provided
        """
        logger.info("Calculating SHAP values")
        
        try:
            # Determine data to use
            if data_sample is not None:
                analysis_data = data_sample
            else:
                # Sample from available data
                analysis_data = self.data.sample(min(sample_size, len(self.data)))
            
            logger.info(f"Analyzing {len(analysis_data)} samples")
            
            # Calculate SHAP values
            if hasattr(self.explainer, 'shap_values'):
                self.shap_values_raw = self.explainer.shap_values(analysis_data.values)
            else:
                self.shap_values_raw = self.explainer(analysis_data.values)
            
            # Handle different SHAP output formats
            if isinstance(self.shap_values_raw, list):
                # For multi-class classification, take the first class
                self.shap_values_array = self.shap_values_raw[0]
            else:
                self.shap_values_array = self.shap_values_raw
            
            # Convert to DataFrame for easier analysis
            self.shap_values_df = pd.DataFrame(
                self.shap_values_array,
                columns=self.feature_names,
                index=analysis_data.index
            )
            
            logger.info(f"SHAP values calculated: {self.shap_values_df.shape}")
            
        except Exception as e:
            logger.error(f"Error calculating SHAP values: {str(e)}")
            raise
    
    def calculate_feature_importance(self) -> pd.DataFrame:
        """
        Calculate feature importance based on SHAP values
        
        Returns:
            pd.DataFrame: Feature importance rankings
        """
        logger.info("Calculating feature importance from SHAP values")
        
        if self.shap_values_df is None:
            raise ValueError("SHAP values not calculated. Call calculate_shap_values() first.")
        
        # Calculate mean absolute SHAP values for each feature
        feature_importance = np.abs(self.shap_values_array).mean(0)
        
        # Create DataFrame with feature names and importance
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': feature_importance,
            'mean_shap': self.shap_values_array.mean(0),
            'std_shap': self.shap_values_array.std(0)
        })
        
        # Sort by importance
        importance_df = importance_df.sort_values('importance', ascending=False)
        importance_df['rank'] = range(1, len(importance_df) + 1)
        
        self.feature_importance = importance_df
        logger.info(f"Feature importance calculated for {len(importance_df)} features")
        
        return importance_df
    
    def plot_feature_importance(self, top_n: int = 20, save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot feature importance chart
        
        Args:
            top_n (int): Number of top features to show
            save_path (str): Path to save plot (optional)
            
        Returns:
            plt.Figure: Generated figure
        """
        logger.info(f"Plotting feature importance for top {top_n} features")
        
        if self.feature_importance is None or len(self.feature_importance) == 0:
            raise ValueError("Feature importance not calculated. Call calculate_feature_importance() first.")
        
        # Select top N features
        top_features = self.feature_importance.head(top_n)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, max(8, top_n * 0.3)))
        
        # Create horizontal bar chart
        y_pos = np.arange(len(top_features))
        bars = ax.barh(y_pos, top_features['importance'], 
                      color=plt.cm.viridis(np.linspace(0, 1, len(top_features))))
        
        # Add value labels
        for i, (bar, importance) in enumerate(zip(bars, top_features['importance'])):
            ax.text(bar.get_width() + importance * 0.01, bar.get_y() + bar.get_height()/2,
                   f'{importance:.3f}', ha='left', va='center', fontsize=9)
        
        # Labels and formatting
        ax.set_yticks(y_pos)
        ax.set_yticklabels(top_features['feature'])
        ax.set_xlabel('Mean |SHAP Value| (Average Impact on Model Output)')
        ax.set_title('Feature Importance (SHAP Values)', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Feature importance plot saved to {save_path}")
        
        return fig
    
    def plot_shap_summary(self, save_path: Optional[str] = None) -> plt.Figure:
        """
        Create SHAP summary plot
        
        Args:
            save_path (str): Path to save plot (optional)
            
        Returns:
            plt.Figure: Generated figure
        """
        logger.info("Creating SHAP summary plot")
        
        if self.shap_values_array is None or self.data is None:
            raise ValueError("SHAP values or data not available. Call calculate_shap_values() first.")
        
        # Create SHAP summary plot
        fig = plt.figure(figsize=(12, 10))
        
        # Use SHAP's built-in plotting function
        shap.summary_plot(
            self.shap_values_array, 
            self.data.values,
            feature_names=self.feature_names,
            show=False,
            plot_size=(12, 10)
        )
        
        plt.title('SHAP Summary Plot', fontsize=16, pad=20)
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"SHAP summary plot saved to {save_path}")
        
        return fig
    
    def plot_dependence(self, feature_name: str, interaction_index: Optional[str] = None, 
                       save_path: Optional[str] = None) -> plt.Figure:
        """
        Create SHAP dependence plot for a specific feature
        
        Args:
            feature_name (str): Name of feature to analyze
            interaction_index (str): Feature to color by (optional)
            save_path (str): Path to save plot (optional)
            
        Returns:
            plt.Figure: Generated figure
        """
        logger.info(f"Creating SHAP dependence plot for {feature_name}")
        
        if self.shap_values_array is None or self.data is None:
            raise ValueError("SHAP values or data not available. Call calculate_shap_values() first.")
        
        # Find feature index
        if feature_name not in self.feature_names:
            raise ValueError(f"Feature {feature_name} not found in feature names")
        
        feature_index = self.feature_names.index(feature_name)
        
        # Create dependence plot
        fig = plt.figure(figsize=(10, 8))
        
        shap.dependence_plot(
            feature_index,
            self.shap_values_array,
            self.data.values,
            feature_names=self.feature_names,
            interaction_index=interaction_index,
            show=False
        )
        
        plt.title(f'SHAP Dependence Plot: {feature_name}', fontsize=16, pad=20)
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"SHAP dependence plot saved to {save_path}")
        
        return fig
    
    def generate_interpretation_report(self) -> Dict:
        """
        Generate interpretation report with key insights
        
        Returns:
            Dict: Interpretation report with insights
        """
        logger.info("Generating interpretation report")
        
        if self.feature_importance is None or len(self.feature_importance) == 0:
            raise ValueError("Feature importance not calculated. Call calculate_feature_importance() first.")
        
        report = {
            'top_features': [],
            'key_insights': [],
            'feature_impact_direction': [],
            'model_interpretability': {}
        }
        
        # Top positive impacting features
        top_positive = self.feature_importance.nlargest(10, 'mean_shap')
        report['top_features'].extend([
            f"{row['feature']}: {row['mean_shap']:.4f}" 
            for _, row in top_positive.iterrows()
        ])
        
        # Top negative impacting features (if any)
        top_negative = self.feature_importance.nsmallest(10, 'mean_shap')
        if (top_negative['mean_shap'] < 0).any():
            report['top_features'].extend([
                f"{row['feature']}: {row['mean_shap']:.4f}" 
                for _, row in top_negative.iterrows() if row['mean_shap'] < 0
            ])
        
        # Key insights based on feature importance
        if len(self.feature_importance) >= 5:
            top_5 = self.feature_importance.head(5)
            report['key_insights'].append(
                f"Top influencing factor: {top_5.iloc[0]['feature']} "
                f"(impact: {top_5.iloc[0]['mean_shap']:.4f})"
            )
            
            # Feature impact direction
            positive_impact = self.feature_importance[self.feature_importance['mean_shap'] > 0]
            negative_impact = self.feature_importance[self.feature_importance['mean_shap'] < 0]
            
            report['feature_impact_direction'] = {
                'positive_features': len(positive_impact),
                'negative_features': len(negative_impact),
                'neutral_features': len(self.feature_importance) - len(positive_impact) - len(negative_impact)
            }
            
            # Model interpretability metrics
            total_importance = self.feature_importance['importance'].sum()
            top_10_importance = self.feature_importance.head(10)['importance'].sum()
            
            report['model_interpretability'] = {
                'total_features': len(self.feature_importance),
                'explained_variance_ratio': top_10_importance / total_importance if total_importance > 0 else 0,
                'top_features_cumulative_importance': top_10_importance
            }
        
        logger.info("Interpretation report generated")
        return report
    
    def save_analysis_results(self, output_dir: str = "results/feature_analysis"):
        """
        Save all analysis results
        
        Args:
            output_dir (str): Directory to save results
        """
        logger.info(f"Saving analysis results to {output_dir}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save feature importance
        if self.feature_importance is not None:
            importance_path = os.path.join(output_dir, "feature_importance.csv")
            self.feature_importance.to_csv(importance_path, index=False)
            logger.info(f"Feature importance saved to {importance_path}")
        
        # Save SHAP values
        if self.shap_values_df is not None:
            shap_values_path = os.path.join(output_dir, "shap_values.csv")
            self.shap_values_df.to_csv(shap_values_path, index=False)
            logger.info(f"SHAP values saved to {shap_values_path}")
        
        # Save interpretation report
        interpretation_report = self.generate_interpretation_report()
        report_path = os.path.join(output_dir, "interpretation_report.json")
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(interpretation_report, f, indent=2, ensure_ascii=False)
        logger.info(f"Interpretation report saved to {report_path}")
        
        # Save plots
        try:
            # Feature importance plot
            fig1 = self.plot_feature_importance(top_n=20)
            importance_plot_path = os.path.join(output_dir, "feature_importance.png")
            fig1.savefig(importance_plot_path, dpi=300, bbox_inches='tight')
            plt.close(fig1)
            logger.info(f"Feature importance plot saved to {importance_plot_path}")
            
            # SHAP summary plot
            fig2 = self.plot_shap_summary()
            summary_plot_path = os.path.join(output_dir, "shap_summary.png")
            fig2.savefig(summary_plot_path, dpi=300, bbox_inches='tight')
            plt.close(fig2)
            logger.info(f"SHAP summary plot saved to {summary_plot_path}")
            
        except Exception as e:
            logger.warning(f"Could not save plots: {str(e)}")
        
        logger.info("Analysis results saved successfully")

def main():
    """
    Main function to test SHAP analysis
    """
    logger.info("Testing SHAP feature importance analysis")
    
    print("SHAP Feature Importance Analysis Module")
    print("This module analyzes trained demand prediction models using SHAP values.")

if __name__ == "__main__":
    main()