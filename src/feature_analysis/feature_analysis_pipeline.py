"""
Feature Analysis Pipeline
Orchestrates complete feature importance and SHAP analysis workflow
"""
import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, List, Tuple, Any, Optional

from .shap_analysis import SHAPFeatureAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureAnalysisPipeline:
    """
    Main pipeline for feature importance and SHAP analysis
    """
    
    def __init__(self, models_dir: str = "models", data_dir: str = "results/processed_data"):
        """
        Initialize the feature analysis pipeline
        
        Args:
            models_dir (str): Directory containing trained models
            data_dir (str): Directory containing processed data
        """
        self.models_dir = models_dir
        self.data_dir = data_dir
        self.analyzers = {}
        self.results = {}
    
    def load_trained_models(self) -> List[str]:
        """
        Load available trained models
        
        Returns:
            List[str]: List of available model names
        """
        logger.info(f"Loading trained models from {self.models_dir}")
        
        if not os.path.exists(self.models_dir):
            logger.warning(f"Models directory not found: {self.models_dir}")
            return []
        
        # Find model files
        model_files = []
        for file in os.listdir(self.models_dir):
            if file.endswith(('.joblib', '.pkl', '.h5', '.hdf5')):
                model_files.append(file)
        
        logger.info(f"Found {len(model_files)} trained models")
        return model_files
    
    def load_processed_data(self) -> pd.DataFrame:
        """
        Load processed data for analysis
        
        Returns:
            pd.DataFrame: Processed data for feature analysis
        """
        logger.info(f"Loading processed data from {self.data_dir}")
        
        # Load features data
        features_path = os.path.join(self.data_dir, "features.csv")
        if os.path.exists(features_path):
            features_df = pd.read_csv(features_path)
            logger.info(f"Loaded features data: {len(features_df)} rows, {len(features_df.columns)} columns")
            return features_df
        else:
            logger.warning(f"Features file not found: {features_path}")
            # Try other data files
            for file in os.listdir(self.data_dir):
                if file.endswith('.csv'):
                    filepath = os.path.join(self.data_dir, file)
                    df = pd.read_csv(filepath)
                    logger.info(f"Loaded {file}: {len(df)} rows, {len(df.columns)} columns")
                    return df
        
        raise FileNotFoundError(f"No data files found in {self.data_dir}")
    
    def analyze_xgboost_model(self, model_file: str = None) -> Dict:
        """
        Analyze XGBoost model using SHAP
        
        Args:
            model_file (str): Specific model file to analyze (optional)
            
        Returns:
            Dict: Analysis results
        """
        logger.info("Analyzing XGBoost model with SHAP")
        
        try:
            # Determine model file
            if model_file is None:
                # Look for XGBoost model files
                xgb_files = [f for f in os.listdir(self.models_dir) 
                            if 'xgboost' in f.lower() and f.endswith('.joblib')]
                if xgb_files:
                    model_file = xgb_files[0]
                else:
                    raise FileNotFoundError("No XGBoost model file found")
            
            model_path = os.path.join(self.models_dir, model_file)
            data_path = self.data_dir
            
            # Initialize analyzer
            analyzer = SHAPFeatureAnalyzer()
            
            # Load model and data
            analyzer.load_model_and_data(model_path, data_path, 'xgboost')
            
            # Load data for analysis
            data_df = self.load_processed_data()
            
            # Create SHAP explainer
            analyzer.create_shap_explainer('xgboost', data_df)
            
            # Calculate SHAP values
            analyzer.calculate_shap_values(data_df, sample_size=500)  # Smaller sample for faster analysis
            
            # Calculate feature importance
            importance_df = analyzer.calculate_feature_importance()
            
            # Store results
            self.analyzers['xgboost'] = analyzer
            self.results['xgboost'] = {
                'feature_importance': importance_df,
                'shap_values': analyzer.shap_values_df if hasattr(analyzer, 'shap_values_df') else None,
                'model_file': model_file
            }
            
            logger.info("XGBoost model analysis completed successfully")
            return self.results['xgboost']
            
        except Exception as e:
            logger.error(f"Error analyzing XGBoost model: {str(e)}")
            raise
    
    def analyze_deep_learning_model(self, model_type: str = 'lstm', model_file: str = None) -> Dict:
        """
        Analyze deep learning model using SHAP
        
        Args:
            model_type (str): Type of model ('lstm' or 'gru')
            model_file (str): Specific model file to analyze (optional)
            
        Returns:
            Dict: Analysis results
        """
        logger.info(f"Analyzing {model_type.upper()} model with SHAP")
        
        try:
            # Determine model file
            if model_file is None:
                # Look for LSTM/GRU model files
                dl_files = [f for f in os.listdir(self.models_dir) 
                           if model_type.lower() in f.lower() and f.endswith(('.h5', '.hdf5'))]
                if dl_files:
                    model_file = dl_files[0]
                else:
                    raise FileNotFoundError(f"No {model_type.upper()} model file found")
            
            model_path = os.path.join(self.models_dir, model_file)
            data_path = self.data_dir
            
            # Initialize analyzer
            analyzer = SHAPFeatureAnalyzer()
            
            # Load model and data
            analyzer.load_model_and_data(model_path, data_path, model_type.lower())
            
            # Load data for analysis
            data_df = self.load_processed_data()
            
            # Create SHAP explainer
            analyzer.create_shap_explainer(model_type.lower(), data_df)
            
            # Calculate SHAP values
            analyzer.calculate_shap_values(data_df, sample_size=200)  # Even smaller sample for DL models
            
            # Calculate feature importance
            importance_df = analyzer.calculate_feature_importance()
            
            # Store results
            self.analyzers[model_type.lower()] = analyzer
            self.results[model_type.lower()] = {
                'feature_importance': importance_df,
                'shap_values': analyzer.shap_values_df if hasattr(analyzer, 'shap_values_df') else None,
                'model_file': model_file
            }
            
            logger.info(f"{model_type.upper()} model analysis completed successfully")
            return self.results[model_type.lower()]
            
        except Exception as e:
            logger.error(f"Error analyzing {model_type.upper()} model: {str(e)}")
            raise
    
    def compare_feature_importance(self) -> pd.DataFrame:
        """
        Compare feature importance across different models
        
        Returns:
            pd.DataFrame: Comparison of feature importance
        """
        logger.info("Comparing feature importance across models")
        
        if not self.results:
            raise ValueError("No analysis results available. Run analysis first.")
        
        # Collect feature importance from all models
        comparison_data = []
        
        for model_name, results in self.results.items():
            if 'feature_importance' in results and results['feature_importance'] is not None:
                importance_df = results['feature_importance']
                for _, row in importance_df.head(15).iterrows():  # Top 15 features
                    comparison_data.append({
                        'model': model_name.upper(),
                        'feature': row['feature'],
                        'importance': row['importance'],
                        'mean_shap': row['mean_shap'],
                        'rank': row['rank']
                    })
        
        if not comparison_data:
            logger.warning("No feature importance data available for comparison")
            return pd.DataFrame()
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Pivot for easier comparison
        pivot_df = comparison_df.pivot_table(
            index='feature', 
            columns='model', 
            values='importance', 
            fill_value=0
        ).reset_index()
        
        # Add rank information
        pivot_df = pivot_df.sort_values(by=list(pivot_df.columns[1:]), ascending=False)
        
        logger.info("Feature importance comparison completed")
        return pivot_df
    
    def generate_insights_report(self) -> Dict:
        """
        Generate comprehensive insights report
        
        Returns:
            Dict: Insights report with key findings
        """
        logger.info("Generating insights report")
        
        report = {
            'executive_summary': {},
            'model_comparison': {},
            'key_findings': [],
            'recommendations': [],
            'technical_details': {}
        }
        
        # Executive summary
        total_models = len(self.results)
        report['executive_summary'] = {
            'models_analyzed': total_models,
            'total_features': sum(len(res.get('feature_importance', [])) 
                                for res in self.results.values() if res.get('feature_importance') is not None),
            'analysis_status': 'completed' if total_models > 0 else 'incomplete'
        }
        
        # Model comparison
        report['model_comparison'] = self.compare_feature_importance()
        
        # Key findings from each model
        for model_name, results in self.results.items():
            if 'feature_importance' in results and results['feature_importance'] is not None:
                top_features = results['feature_importance'].head(5)
                report['key_findings'].append({
                    'model': model_name.upper(),
                    'top_features': top_features[['feature', 'importance']].to_dict('records'),
                    'total_features': len(results['feature_importance'])
                })
        
        # Recommendations based on findings
        if report['key_findings']:
            report['recommendations'].append("Focus on top influencing features for demand prediction")
            report['recommendations'].append("Consider feature engineering for high-importance features")
            report['recommendations'].append("Monitor feature drift for model maintenance")
        
        # Technical details
        report['technical_details'] = {
            'shap_methodology': 'TreeExplainer for XGBoost, DeepExplainer for deep learning models',
            'sample_sizes': {model: len(res.get('shap_values', [])) 
                           for model, res in self.results.items() if res.get('shap_values') is not None},
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }
        
        logger.info("Insights report generated successfully")
        return report
    
    def save_analysis_results(self, output_dir: str = "results/feature_analysis"):
        """
        Save all analysis results and visualizations
        
        Args:
            output_dir (str): Directory to save results
        """
        logger.info(f"Saving analysis results to {output_dir}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save results from each analyzer
        for model_name, analyzer in self.analyzers.items():
            model_output_dir = os.path.join(output_dir, model_name)
            try:
                analyzer.save_analysis_results(model_output_dir)
                logger.info(f"Saved {model_name} analysis results to {model_output_dir}")
            except Exception as e:
                logger.warning(f"Could not save {model_name} results: {str(e)}")
        
        # Save comparison results
        try:
            comparison_df = self.compare_feature_importance()
            if not comparison_df.empty:
                comparison_path = os.path.join(output_dir, "feature_importance_comparison.csv")
                comparison_df.to_csv(comparison_path, index=False)
                logger.info(f"Feature importance comparison saved to {comparison_path}")
        except Exception as e:
            logger.warning(f"Could not save comparison results: {str(e)}")
        
        # Save insights report
        try:
            insights_report = self.generate_insights_report()
            report_path = os.path.join(output_dir, "insights_report.json")
            import json
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(insights_report, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"Insights report saved to {report_path}")
        except Exception as e:
            logger.warning(f"Could not save insights report: {str(e)}")
        
        logger.info("All analysis results saved successfully")
    
    def run_pipeline(self, analyze_xgboost: bool = True, analyze_lstm: bool = False, 
                    analyze_gru: bool = False) -> Dict:
        """
        Run complete feature analysis pipeline
        
        Args:
            analyze_xgboost (bool): Whether to analyze XGBoost model
            analyze_lstm (bool): Whether to analyze LSTM model
            analyze_gru (bool): Whether to analyze GRU model
            
        Returns:
            Dict: Complete pipeline results
        """
        logger.info("Starting feature analysis pipeline")
        
        try:
            # Load trained models
            available_models = self.load_trained_models()
            logger.info(f"Available models: {available_models}")
            
            # Load processed data
            data_df = self.load_processed_data()
            
            # Analyze models
            if analyze_xgboost:
                try:
                    xgb_results = self.analyze_xgboost_model()
                    logger.info("XGBoost model analysis completed")
                except Exception as e:
                    logger.warning(f"XGBoost analysis failed: {str(e)}")
            
            if analyze_lstm:
                try:
                    lstm_results = self.analyze_deep_learning_model('lstm')
                    logger.info("LSTM model analysis completed")
                except Exception as e:
                    logger.warning(f"LSTM analysis failed: {str(e)}")
            
            if analyze_gru:
                try:
                    gru_results = self.analyze_deep_learning_model('gru')
                    logger.info("GRU model analysis completed")
                except Exception as e:
                    logger.warning(f"GRU analysis failed: {str(e)}")
            
            # Generate insights report
            insights_report = self.generate_insights_report()
            
            # Save results
            self.save_analysis_results()
            
            # Prepare final results
            pipeline_results = {
                'analysis_results': self.results,
                'insights_report': insights_report,
                'model_comparison': self.compare_feature_importance(),
                'available_models': available_models
            }
            
            logger.info("Feature analysis pipeline completed successfully")
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Error in feature analysis pipeline: {str(e)}")
            raise

def main():
    """
    Main function to run the feature analysis pipeline
    """
    logger.info("=== Feature Analysis Pipeline ===")
    
    # Initialize pipeline
    pipeline = FeatureAnalysisPipeline()
    
    try:
        # Run the complete pipeline
        results = pipeline.run_pipeline(
            analyze_xgboost=True,
            analyze_lstm=False,  # Skip for faster execution
            analyze_gru=False    # Skip for faster execution
        )
        
        # Print summary
        print("\n=== Feature Analysis Summary ===")
        print(f"Models analyzed: {len(results['analysis_results'])}")
        print(f"Available models: {len(results['available_models'])}")
        
        # Print key findings
        if 'insights_report' in results and results['insights_report']['key_findings']:
            print(f"\nKey Findings:")
            for finding in results['insights_report']['key_findings']:
                print(f"  {finding['model']} Model:")
                for feature in finding['top_features'][:3]:  # Top 3 features
                    print(f"    - {feature['feature']}: {feature['importance']:.4f}")
        
        # Print recommendations
        if 'insights_report' in results and results['insights_report']['recommendations']:
            print(f"\nRecommendations:")
            for rec in results['insights_report']['recommendations'][:3]:
                print(f"  - {rec}")
        
        print(f"\nAnalysis results saved to 'results/feature_analysis' directory")
        print("Pipeline completed successfully!")
        
    except Exception as e:
        logger.error(f"Error running feature analysis pipeline: {str(e)}")
        raise

if __name__ == "__main__":
    main()