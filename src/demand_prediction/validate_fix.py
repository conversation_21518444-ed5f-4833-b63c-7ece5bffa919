"""
Validation script to test that the data preprocessing fix is working correctly
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

import pandas as pd
import numpy as np
from src.demand_prediction.data_preparation import prepare_dataset
from src.demand_prediction.xgboost_model import train_xgboost_model
from src.demand_prediction.lstm_model import prepare_lstm_dataset, train_lstm_model

def validate_data_fix():
    """Validate that the data preprocessing fix is working correctly"""
    print("=== Validating Data Preprocessing Fix ===")
    
    # Load and prepare dataset
    print("1. Loading and preparing dataset...")
    dataset = prepare_dataset("results/processed_data")
    
    print(f"   Training samples: {len(dataset['X_train'])}")
    print(f"   Test samples: {len(dataset['X_test'])}")
    print(f"   Features: {len(dataset['feature_names'])}")
    print(f"   Training target stats: mean={dataset['y_train'].mean():.2f}, std={dataset['y_train'].std():.2f}")
    print(f"   Test target stats: mean={dataset['y_test'].mean():.2f}, std={dataset['y_test'].std():.2f}")
    
    # Check that target variable has reasonable scale
    train_mean = dataset['y_train'].mean()
    if train_mean < 10 or train_mean > 200:
        print(f"   ⚠️  Warning: Target variable mean ({train_mean:.2f}) seems unusual")
    else:
        print(f"   ✅ Target variable has reasonable scale")
    
    # Test XGBoost model
    print("\n2. Testing XGBoost model...")
    try:
        xgboost_model, xgboost_results = train_xgboost_model(dataset)
        print(f"   XGBoost R² score: {xgboost_results['test_r2']:.4f}")
        if xgboost_results['test_r2'] > 0.8:
            print("   ✅ XGBoost model performing well")
        else:
            print("   ⚠️  XGBoost model performance below expectations")
    except Exception as e:
        print(f"   ❌ XGBoost test failed: {str(e)}")
    
    # Test LSTM model
    print("\n3. Testing LSTM model...")
    try:
        # Prepare LSTM dataset
        lstm_dataset = prepare_lstm_dataset(dataset, sequence_length=5)
        print(f"   LSTM sequences created: {len(lstm_dataset['X_train'])}")
        
        # Train LSTM model (reduced epochs for quick test)
        lstm_model, lstm_results = train_lstm_model(lstm_dataset, model_type='LSTM')
        lstm_r2 = lstm_results['test_metrics']['r2_score']
        print(f"   LSTM R² score: {lstm_r2:.4f}")
        if lstm_r2 > 0.8:
            print("   ✅ LSTM model performing well")
        else:
            print("   ⚠️  LSTM model performance below expectations")
    except Exception as e:
        print(f"   ❌ LSTM test failed: {str(e)}")
    
    print("\n=== Validation Complete ===")

if __name__ == "__main__":
    validate_data_fix()