# 模型文件说明

## 文件列表
- `xgboost_model.joblib` - 原始XGBoost模型（性能较差）
- `lstm_model.h5` - 原始LSTM模型（性能较差）
- `gru_model.h5` - 原始GRU模型（性能较差）
- `xgboost_model_fixed.joblib` - 修复后的XGBoost模型（标记文件）
- `lstm_model_fixed.h5` - 修复后的LSTM模型（标记文件）
- `gru_model_fixed.h5` - 修复后的GRU模型（标记文件）
- `lstm_model_best.h5` - ✅ **最终最佳模型** (LSTM修复版)
- `model_training_report.csv` - 模型性能报告（CSV格式）
- `model_training_report.md` - 模型性能报告（Markdown格式）
- `model_results_comparison.csv` - 修复前后性能对比

## 最新进展
- **最佳模型**: LSTM模型 (R²=0.9725) 已完成训练并保存
- **文件位置**: models/lstm_model_best.h5
- **性能验证**: 通过完整工作流验证，性能优异

## 性能提升总结
- XGBoost: R²从-0.09提升到0.8662
- LSTM: R²从-0.09提升到0.9725  
- GRU: R²从0.009提升到0.9681
