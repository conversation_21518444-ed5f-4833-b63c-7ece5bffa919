{"timestamp": "2025-07-30T16:06:35.831836", "system_info": {"platform": "Linux-4.15.0-142-generic-x86_64-with-debian-bullseye-sid", "python_version": "3.7.3", "cpu_count": 72, "memory_total_gb": 251.56, "disk_free_gb": 1950.78, "timestamp": "2025-07-30T16:06:35.831530"}, "log_file": "results/logs/workflow_execution_20250730_160635.log", "steps": {"data_preprocessing": {"status": "success", "duration_seconds": 1.95, "start_time": "2025-07-30T16:06:35.831979", "end_time": "2025-07-30T16:06:37.781239", "error_message": null, "message": "数据预处理完成", "input_files": {"data_fam_schedule.csv": {"exists": true, "size_mb": 0.03}, "data_fam_fleet.csv": {"exists": true, "size_mb": 0.0}, "data_fam_products.csv": {"exists": true, "size_mb": 5.04}, "data_fam_market_share.csv": {"exists": true, "size_mb": 0.02}}, "output_files": {"features.csv": {"exists": true, "size_mb": 0.08, "rows": 815, "columns": 14}, "schedule.csv": {"exists": true, "size_mb": 0.07, "rows": 815, "columns": 18}, "products.csv": {"exists": true, "size_mb": 7.61, "rows": 47190, "columns": 38}, "fleet.csv": {"exists": true, "size_mb": 0.0, "rows": 9, "columns": 4}, "market_share.csv": {"exists": true, "size_mb": 0.02, "rows": 819, "columns": 3}}, "processing_duration": 0.92, "save_duration": 0.88, "memory_usage": {"before": {"memory_percent": 6.0, "memory_used_gb": 13.23, "memory_total_gb": 251.56}, "after": {"memory_percent": 6.0, "memory_used_gb": 13.26, "memory_total_gb": 251.56}}}, "demand_prediction": {"status": "success", "duration_seconds": 48.25, "start_time": "2025-07-30T16:06:37.781399", "end_time": "2025-07-30T16:07:26.030232", "error_message": null, "model_used": "XGBoost (最佳模型)", "best_metrics": {"r2_score": 0.9998912458594615, "mse": 0.2620922155164148, "mae": 0.3581522550356182, "rmse": 0.5119494267175371, "mape": 3961251.384462326}, "all_models": {"LSTM": {"model_type": "LSTM", "training_duration": 21.32, "timestamp": "2025-07-30T16:07:00.697650", "metrics": {"r2_score": 0.0814497550075215, "mse": 2318.9266332291786, "mae": 40.03746350057764, "rmse": 48.15523474378646, "mape": 5302839435.982696}, "model_file": "models/lstm_model_enhanced.h5", "training_history": {}}, "GRU": {"model_type": "GRU", "training_duration": 22.88, "timestamp": "2025-07-30T16:07:23.577987", "metrics": {"r2_score": 0.0196949635648056, "mse": 2474.8297331267786, "mae": 42.07662644130731, "rmse": 49.74766057943608, "mape": 5462925539.767897}, "model_file": "models/gru_model_enhanced.h5", "training_history": {}}, "XGBoost": {"model_type": "XGBoost", "training_duration": 2.45, "timestamp": "2025-07-30T16:07:26.027282", "metrics": {"r2_score": 0.9998912458594615, "mse": 0.2620922155164148, "mae": 0.3581522550356182, "rmse": 0.5119494267175371, "mape": 3961251.384462326}, "model_file": "models/xgboost_model_enhanced.joblib", "training_history": {}}}, "dataset_preparation_duration": 0.19, "lstm_preparation_duration": 0.0, "data_samples": {"train": 652, "validation": 0, "test": 163, "lstm_sequences": 513}, "memory_usage": {"before": {"memory_percent": 6.0, "memory_used_gb": 13.36, "memory_total_gb": 251.56}, "after": {"memory_percent": 6.1, "memory_used_gb": 13.56, "memory_total_gb": 251.56}}}, "fleet_optimization": {"status": "failed", "duration_seconds": 0.0, "start_time": "2025-07-30T16:07:26.031250", "end_time": "2025-07-30T16:07:26.036323", "error_message": "舰队分配优化失败: cannot import name 'Literal' from 'typing' (/home/<USER>/lib/python3.7/typing.py)"}, "analysis": {"status": "success", "duration_seconds": 2.04, "start_time": "2025-07-30T16:07:26.037018", "end_time": "2025-07-30T16:07:28.079221", "error_message": null, "message": "分析评估完成", "analysis_stats": {"total_metrics": 3, "analysis_types": ["financial", "operational", "timestamp"], "duration": 2.04}, "key_metrics": {"financial": {"baselines": {"pre_merger": {"scenario": "pre_merger", "total_flights": 480, "total_revenue": 12000000, "total_costs": 7200000, "total_profit": 4800000, "profit_margin": 0.4}, "naive_post_merger": {"scenario": "naive_post_merger", "total_flights": 815, "total_revenue": 18745000, "total_costs": 13040000, "total_profit": 5705000, "profit_margin": 0.30434782608695654}}, "optimized": {"scenario": "optimized", "total_flights": 739, "total_revenue": 10464663.9999988, "total_costs": 7505546.983333329, "total_profit": 2959117.0166654717, "profit_margin": 0.2827722912714456, "assignments": {"AA0001": "F8C12Y126", "AA0002": "F12C0Y132", "AA0003": "F8C12Y99", "AA0004": "F12C12Y48", "AA0005": "F0C0Y72", "AA0006": "F0C0Y76", "AA0007": "F12C0Y112", "AA0008": "F12C30Y117", "AA0011": "F16C0Y165", "AA0012": "F8C12Y126", "AA0014": "F12C0Y132", "AA0015": "F8C12Y99", "AA0016": "F12C12Y48", "AA0017": "F0C0Y72", "AA0018": "F0C0Y76", "AA0019": "F12C0Y112", "AA0020": "F12C30Y117", "AA0021": "F16C0Y165", "AA0022": "F8C12Y126", "AA0023": "F12C0Y132", "AA0024": "F8C12Y99", "AA0025": "F12C12Y48", "AA0026": "F0C0Y72", "AA0029": "F0C0Y76", "AA0030": "F12C0Y112", "AA0035": "F12C30Y117", "AA0036": "F16C0Y165", "AA0037": "F8C12Y126", "AA0038": "F12C0Y132", "AA0039": "F8C12Y99", "AA0040": "F12C12Y48", "AA0041": "F0C0Y72", "AA0042": "F0C0Y76", "AA0043": "F12C0Y112", "AA0044": "F12C30Y117", "AA0045": "F16C0Y165", "AA0046": "F8C12Y126", "AA0047": "F12C0Y132", "AA0048": "F8C12Y99", "AA0049": "F12C12Y48", "AA0051": "F0C0Y72", "AA0052": "F0C0Y76", "AA0053": "F12C0Y112", "AA0054": "F12C30Y117", "AA0055": "F16C0Y165", "AA0056": "F8C12Y126", "AA0059": "F12C0Y132", "AA0060": "F8C12Y99", "AA0061": "F12C12Y48", "AA0062": "F0C0Y72", "AA0064": "F0C0Y76", "AA0065": "F12C0Y112", "AA0066": "F12C30Y117", "AA0067": "F16C0Y165", "AA0069": "F8C12Y126", "AA0070": "F12C0Y132", "AA0072": "F8C12Y99", "AA0073": "F12C12Y48", "AA0075": "F0C0Y72", "AA0076": "F0C0Y76", "AA0079": "F12C0Y112", "AA0080": "F12C30Y117", "AA0081": "F16C0Y165", "AA0082": "F8C12Y126", "AA0083": "F12C0Y132", "AA0084": "F8C12Y99", "AA0086": "F12C12Y48", "AA0087": "F0C0Y72", "AA0089": "F0C0Y76", "AA0090": "F12C0Y112", "AA0092": "F12C30Y117", "AA0093": "F16C0Y165", "AA0094": "F8C12Y126", "AA0095": "F12C0Y132", "AA0096": "F8C12Y99", "AA0097": "F12C12Y48", "AA0098": "F0C0Y72", "AA0099": "F0C0Y76", "AA0101": "F12C0Y112", "AA0103": "F12C30Y117", "AA0104": "F16C0Y165", "AA0106": "F8C12Y126", "AA0107": "F12C0Y132", "AA0108": "F8C12Y99", "AA0109": "F12C12Y48", "AA0111": "F0C0Y72", "AA0112": "F0C0Y76", "AA0114": "F12C0Y112", "AA0117": "F12C30Y117", "AA0119": "F16C0Y165", "AA0120": "F8C12Y126", "AA0121": "F12C0Y132", "AA0122": "F8C12Y99", "AA0123": "F12C12Y48", "AA0124": "F0C0Y72", "AA0125": "F0C0Y76", "AA0126": "F12C0Y112", "AA0127": "F12C30Y117", "AA0128": "F16C0Y165", "AA0129": "F8C12Y126", "AA0132": "F12C0Y132", "AA0134": "F8C12Y99", "AA0135": "F12C12Y48", "AA0136": "F0C0Y72", "AA0137": "F0C0Y76", "AA0138": "F12C0Y112", "AA0139": "F12C30Y117", "AA0143": "F16C0Y165", "AA0151": "F8C12Y126", "AA0152": "F12C0Y132", "AA0153": "F8C12Y99", "AA0181": "F12C12Y48", "AA0182": "F0C0Y72", "AA0183": "F0C0Y76", "AA0184": "F12C0Y112", "AA0187": "F12C30Y117", "AA0188": "F16C0Y165", "AA0189": "F8C12Y126", "AA0190": "F12C0Y132", "AA0191": "F8C12Y99", "AA0192": "F12C12Y48", "AA0195": "F0C0Y72", "AA0211": "F0C0Y76", "AA0212": "F12C0Y112", "AA0220": "F12C30Y117", "AA0223": "F16C0Y165", "AA0225": "F8C12Y126", "AA0226": "F12C0Y132", "AA0227": "F8C12Y99", "AA0231": "F12C12Y48", "AA0232": "F0C0Y72", "AA0233": "F0C0Y76", "AA0234": "F12C0Y112", "AA0242": "F12C30Y117", "AA0243": "F16C0Y165", "AA0244": "F8C12Y126", "AA0245": "F12C0Y132", "AA0248": "F8C12Y99", "AA0249": "F12C12Y48", "AA0250": "F0C0Y72", "AA0251": "F0C0Y76", "AA0252": "F12C0Y112", "AA0254": "F12C30Y117", "AA0255": "F16C0Y165", "AA0256": "F8C12Y126", "AA0259": "F12C0Y132", "AA0300": "F8C12Y99", "AA0302": "F12C12Y48", "AA0303": "F0C0Y72", "AA0304": "F0C0Y76", "AA0305": "F12C0Y112", "AA0307": "F12C30Y117", "AA0312": "F16C0Y165", "AA0313": "F8C12Y126", "AA0315": "F12C0Y132", "AA0316": "F8C12Y99", "AA0317": "F12C12Y48", "AA0318": "F0C0Y72", "AA0320": "F0C0Y76", "AA0323": "F12C0Y112", "AA0324": "F12C30Y117", "AA0325": "F16C0Y165", "AA0326": "F8C12Y126", "AA0327": "F12C0Y132", "AA0328": "F8C12Y99", "AA0329": "F12C12Y48", "AA0342": "F0C0Y72", "AA0343": "F0C0Y76", "AA0344": "F12C0Y112", "AA0346": "F12C30Y117", "AA0347": "F16C0Y165", "AA0349": "F8C12Y126", "AA0354": "F12C0Y132", "AA0357": "F8C12Y99", "AA0362": "F12C12Y48", "AA0363": "F0C0Y72", "AA0365": "F0C0Y76", "AA0368": "F12C0Y112", "AA0372": "F12C30Y117", "AA0373": "F16C0Y165", "AA0385": "F8C12Y126", "AA0388": "F12C0Y132", "AA0400": "F8C12Y99", "AA0401": "F12C12Y48", "AA0403": "F0C0Y72", "AA0404": "F0C0Y76", "AA0450": "F12C0Y112", "AA0451": "F12C30Y117", "AA0452": "F16C0Y165", "AA0453": "F8C12Y126", "AA0454": "F12C0Y132", "AA0456": "F8C12Y99", "AA0457": "F12C12Y48", "AA0458": "F0C0Y72", "AA0460": "F0C0Y76", "AA0461": "F12C0Y112", "AA0462": "F12C30Y117", "AA0463": "F16C0Y165", "AA0465": "F8C12Y126", "AA0467": "F12C0Y132", "AA0469": "F8C12Y99", "AA0470": "F12C12Y48", "AA0471": "F0C0Y72", "AA0472": "F0C0Y76", "AA0474": "F12C0Y112", "AA0476": "F12C30Y117", "AA0477": "F16C0Y165", "AA0479": "F8C12Y126", "AA0480": "F12C0Y132", "AA0481": "F8C12Y99", "AA0482": "F12C12Y48", "AA0483": "F0C0Y72", "AA0484": "F0C0Y76", "AA0485": "F12C0Y112", "AA0487": "F12C30Y117", "AA0488": "F16C0Y165", "AA0491": "F8C12Y126", "AA0493": "F12C0Y132", "AA0494": "F8C12Y99", "AA0495": "F12C12Y48", "AA0498": "F0C0Y72", "AA0501": "F0C0Y76", "AA0502": "F12C0Y112", "AA0503": "F12C30Y117", "AA0504": "F16C0Y165", "AA0505": "F8C12Y126", "AA0507": "F12C0Y132", "AA0508": "F8C12Y99", "AA0511": "F12C12Y48", "AA0512": "F0C0Y72", "AA0514": "F0C0Y76", "AA0515": "F12C0Y112", "AA0516": "F12C30Y117", "AA0517": "F16C0Y165", "AA0518": "F8C12Y126", "AA0520": "F12C0Y132", "AA0521": "F8C12Y99", "AA0522": "F12C12Y48", "AA0524": "F0C0Y72", "AA0527": "F0C0Y76", "AA0529": "F12C0Y112", "AA0530": "F12C30Y117", "AA0531": "F16C0Y165", "AA0533": "F8C12Y126", "AA0536": "F12C0Y132", "AA0538": "F8C12Y99", "AA0539": "F12C12Y48", "AA0549": "F0C0Y72", "AA0558": "F0C0Y76", "AA0560": "F12C0Y112", "AA0561": "F12C30Y117", "AA0562": "F16C0Y165", "AA0563": "F8C12Y126", "AA0564": "F12C0Y132", "AA0565": "F8C12Y99", "AA0566": "F12C12Y48", "AA0568": "F0C0Y72", "AA0569": "F0C0Y76", "AA0573": "F12C0Y112", "AA0574": "F12C30Y117", "AA0575": "F16C0Y165", "AA0576": "F8C12Y126", "AA0578": "F12C0Y132", "AA0579": "F8C12Y99", "AA0580": "F12C12Y48", "AA0581": "F0C0Y72", "AA0582": "F0C0Y76", "AA0583": "F12C0Y112", "AA0584": "F12C30Y117", "AA0585": "F16C0Y165", "AA0587": "F8C12Y126", "AA0590": "F12C0Y132", "AA0600": "F8C12Y99", "AA0603": "F12C12Y48", "AA0604": "F0C0Y72", "AA0605": "F0C0Y76", "AA0606": "F12C0Y112", "AA0607": "F12C30Y117", "AA0608": "F16C0Y165", "AA0609": "F8C12Y126", "AA0610": "F12C0Y132", "AA0611": "F8C12Y99", "AA0612": "F12C12Y48", "AA0613": "F0C0Y72", "AA0614": "F0C0Y76", "AA0615": "F12C0Y112", "AA0618": "F12C30Y117", "AA0619": "F16C0Y165", "AA0620": "F8C12Y126", "AA0621": "F12C0Y132", "AA0622": "F8C12Y99", "AA0623": "F12C12Y48", "AA0625": "F0C0Y72", "AA0626": "F0C0Y76", "AA0628": "F12C0Y112", "AA0629": "F12C30Y117", "AA0630": "F16C0Y165", "AA0631": "F8C12Y126", "AA0632": "F12C0Y132", "AA0633": "F8C12Y99", "AA0634": "F12C12Y48", "AA0635": "F0C0Y72", "AA0636": "F0C0Y76", "AA0637": "F12C0Y112", "AA0638": "F12C30Y117", "AA0641": "F16C0Y165", "AA0642": "F8C12Y126", "AA0645": "F12C0Y132", "AA0646": "F8C12Y99", "AA0647": "F12C12Y48", "AA0648": "F0C0Y72", "AA0649": "F0C0Y76", "AA0650": "F12C0Y112", "AA0657": "F12C30Y117", "AA0659": "F16C0Y165", "AA0660": "F8C12Y126", "AA0661": "F12C0Y132", "AA0663": "F8C12Y99", "AA0664": "F12C12Y48", "AA0665": "F0C0Y72", "AA0668": "F0C0Y76", "AA0670": "F12C0Y112", "AA0671": "F12C30Y117", "AA0672": "F16C0Y165", "AA0673": "F8C12Y126", "AA0676": "F12C0Y132", "AA0678": "F8C12Y99", "AA0679": "F12C12Y48", "AA0683": "F0C0Y72", "AA0685": "F0C0Y76", "AA0686": "F12C0Y112", "AA0690": "F12C30Y117", "AA0691": "F16C0Y165", "AA0693": "F8C12Y126", "AA0694": "F12C0Y132", "AA0698": "F8C12Y99", "AA0699": "F12C12Y48", "AA0703": "F0C0Y72", "AA0704": "F0C0Y76", "AA0706": "F12C0Y112", "AA0707": "F12C30Y117", "AA0708": "F16C0Y165", "AA0709": "F8C12Y126", "AA0711": "F12C0Y132", "AA0712": "F8C12Y99", "AA0730": "F12C12Y48", "AA0731": "F0C0Y72", "AA0742": "F0C0Y76", "AA0743": "F12C0Y112", "AA0754": "F12C30Y117", "AA0755": "F16C0Y165", "AA0758": "F8C12Y126", "AA0759": "F12C0Y132", "AA0809": "F8C12Y99", "AA0810": "F12C12Y48", "AA0812": "F0C0Y72", "AA0820": "F0C0Y76", "AA0821": "F12C0Y112", "AA0823": "F12C30Y117", "AA0825": "F16C0Y165", "AA0828": "F8C12Y126", "AA0830": "F12C0Y132", "AA0835": "F8C12Y99", "AA0836": "F12C12Y48", "AA0837": "F0C0Y72", "AA0838": "F0C0Y76", "AA0841": "F12C0Y112", "AA0843": "F12C30Y117", "AA0845": "F16C0Y165", "AA0846": "F8C12Y126", "AA0848": "F12C0Y132", "AA0850": "F8C12Y99", "AA0851": "F12C12Y48", "AA0852": "F0C0Y72", "AA0854": "F0C0Y76", "AA0858": "F12C0Y112", "AA0859": "F12C30Y117", "AA0860": "F16C0Y165", "AA0861": "F8C12Y126", "AA0862": "F12C0Y132", "AA0865": "F8C12Y99", "AA0867": "F12C12Y48", "AA0870": "F0C0Y72", "AA0871": "F0C0Y76", "AA0875": "F12C0Y112", "AA0877": "F12C30Y117", "AA0878": "F16C0Y165", "AA0879": "F8C12Y126", "AA0890": "F12C0Y132", "AA0891": "F8C12Y99", "AA0892": "F12C12Y48", "AA0893": "F0C0Y72", "AA0894": "F0C0Y76", "AA0895": "F12C0Y112", "AA3294": "F12C30Y117", "AA3295": "F16C0Y165", "AA3296": "F8C12Y126", "AA3297": "F12C0Y132", "AA3298": "F8C12Y99", "AA3299": "F12C12Y48", "AA3451": "F0C0Y72", "AA3452": "F0C0Y76", "AA3453": "F12C0Y112", "AA3454": "F12C30Y117", "AA3455": "F16C0Y165", "AA3460": "F8C12Y126", "AA3461": "F12C0Y132", "AA3462": "F8C12Y99", "AA3463": "F12C12Y48", "AA3464": "F0C0Y72", "AA3465": "F0C0Y76", "AA3472": "F12C0Y112", "AA3473": "F12C30Y117", "AA3474": "F16C0Y165", "AA3475": "F8C12Y126", "AA3476": "F12C0Y132", "AA3477": "F8C12Y99", "AA3478": "F12C12Y48", "AA3479": "F0C0Y72", "AA3481": "F0C0Y76", "AA3482": "F12C0Y112", "AA3488": "F12C30Y117", "AA3490": "F16C0Y165", "AA3491": "F8C12Y126", "BA2001": "F12C0Y132", "BA2002": "F8C12Y99", "BA2011": "F12C12Y48", "BA2014": "F0C0Y72", "BA2015": "F0C0Y76", "BA2018": "F12C0Y112", "BA2019": "F12C30Y117", "BA2024": "F16C0Y165", "BA2026": "F8C12Y126", "BA2033": "F12C0Y132", "BA2034": "F8C12Y99", "BA2039": "F12C12Y48", "BA2040": "F0C0Y72", "BA2042": "F0C0Y76", "BA2043": "F12C0Y112", "BA2044": "F12C30Y117", "BA2045": "F16C0Y165", "BA2046": "F8C12Y126", "BA2047": "F12C0Y132", "BA2048": "F8C12Y99", "BA2049": "F12C12Y48", "BA2051": "F0C0Y72", "BA2052": "F0C0Y76", "BA2055": "F12C0Y112", "BA2056": "F12C30Y117", "BA2057": "F16C0Y165", "BA2061": "F8C12Y126", "BA2064": "F12C0Y132", "BA2068": "F8C12Y99", "BA2069": "F12C12Y48", "BA2071": "F0C0Y72", "BA2076": "F0C0Y76", "BA2077": "F12C0Y112", "BA2078": "F12C30Y117", "BA2079": "F16C0Y165", "BA2081": "F8C12Y126", "BA2082": "F12C0Y132", "BA2083": "F8C12Y99", "BA2086": "F12C12Y48", "BA2087": "F0C0Y72", "BA2091": "F0C0Y76", "BA2092": "F12C0Y112", "BA2093": "F12C30Y117", "BA2094": "F16C0Y165", "BA2095": "F8C12Y126", "BA2096": "F12C0Y132", "BA2098": "F8C12Y99", "BA2100": "F12C12Y48", "BA2101": "F0C0Y72", "BA2102": "F0C0Y76", "BA2103": "F12C0Y112", "BA2104": "F12C30Y117", "BA2105": "F16C0Y165", "BA2106": "F8C12Y126", "BA2107": "F12C0Y132", "BA2108": "F8C12Y99", "BA2109": "F12C12Y48", "BA2110": "F0C0Y72", "BA2111": "F0C0Y76", "BA2114": "F12C0Y112", "BA2115": "F12C30Y117", "BA2116": "F16C0Y165", "BA2117": "F8C12Y126", "BA2118": "F12C0Y132", "BA2119": "F8C12Y99", "BA2121": "F12C12Y48", "BA2129": "F0C0Y72", "BA2130": "F0C0Y76", "BA2132": "F12C0Y112", "BA2133": "F12C30Y117", "BA2134": "F16C0Y165", "BA2135": "F8C12Y126", "BA2136": "F12C0Y132", "BA2138": "F8C12Y99", "BA2140": "F12C12Y48", "BA2142": "F0C0Y72", "BA2148": "F0C0Y76", "BA2151": "F12C0Y112", "BA2155": "F12C30Y117", "BA2158": "F16C0Y165", "BA2159": "F8C12Y126", "BA2162": "F12C0Y132", "BA2167": "F8C12Y99", "BA2168": "F12C12Y48", "BA2170": "F0C0Y72", "BA2172": "F0C0Y76", "BA2174": "F12C0Y112", "BA2175": "F12C30Y117", "BA2179": "F16C0Y165", "BA2181": "F8C12Y126", "BA2192": "F12C0Y132", "BA2193": "F8C12Y99", "BA2196": "F12C12Y48", "BA2199": "F0C0Y72", "BA2202": "F0C0Y76", "BA2203": "F12C0Y112", "BA2204": "F12C30Y117", "BA2207": "F16C0Y165", "BA2208": "F8C12Y126", "BA2213": "F12C0Y132", "BA2214": "F8C12Y99", "BA2215": "F12C12Y48", "BA2216": "F0C0Y72", "BA2217": "F0C0Y76", "BA2218": "F12C0Y112", "BA2219": "F12C30Y117", "BA2220": "F16C0Y165", "BA2222": "F8C12Y126", "BA2223": "F12C0Y132", "BA2225": "F8C12Y99", "BA2227": "F12C12Y48", "BA2231": "F0C0Y72", "BA2232": "F0C0Y76", "BA2233": "F12C0Y112", "BA2234": "F12C30Y117", "BA2235": "F16C0Y165", "BA2236": "F8C12Y126", "BA2237": "F12C0Y132", "BA2239": "F8C12Y99", "BA2241": "F12C12Y48", "BA2242": "F0C0Y72", "BA2243": "F0C0Y76", "BA2246": "F12C0Y112", "BA2248": "F12C30Y117", "BA2249": "F16C0Y165", "BA2250": "F8C12Y126", "BA2255": "F12C0Y132", "BA2259": "F8C12Y99", "BA2264": "F12C12Y48", "BA2265": "F0C0Y72", "BA2266": "F0C0Y76", "BA2267": "F12C0Y112", "BA2268": "F12C30Y117", "BA2269": "F16C0Y165", "BA2270": "F8C12Y126", "BA2273": "F12C0Y132", "BA2278": "F8C12Y99", "BA2280": "F12C12Y48", "BA2281": "F0C0Y72", "BA2284": "F0C0Y76", "BA2285": "F12C0Y112", "BA2288": "F12C30Y117", "BA2289": "F16C0Y165", "BA2294": "F8C12Y126", "BA2295": "F12C0Y132", "BA2296": "F8C12Y99", "BA2297": "F12C12Y48", "BA2298": "F0C0Y72", "BA2299": "F0C0Y76", "BA2301": "F12C0Y112", "BA2302": "F12C30Y117", "BA2311": "F16C0Y165", "BA2314": "F8C12Y126", "BA2321": "F12C0Y132", "BA2324": "F8C12Y99", "BA2325": "F12C12Y48", "BA2326": "F0C0Y72", "BA2327": "F0C0Y76", "BA2328": "F12C0Y112", "BA2329": "F12C30Y117", "BA2331": "F16C0Y165", "BA2332": "F8C12Y126", "BA2338": "F12C0Y132", "BA2339": "F8C12Y99", "BA2340": "F12C12Y48", "BA2347": "F0C0Y72", "BA2348": "F0C0Y76", "BA2349": "F12C0Y112", "BA2350": "F12C30Y117", "BA2351": "F16C0Y165", "BA2352": "F8C12Y126", "BA2353": "F12C0Y132", "BA2354": "F8C12Y99", "BA2355": "F12C12Y48", "BA2356": "F0C0Y72", "BA2357": "F0C0Y76", "BA2358": "F12C0Y112", "BA2359": "F12C30Y117", "BA2363": "F16C0Y165", "BA2368": "F8C12Y126", "BA2369": "F12C0Y132", "BA2370": "F8C12Y99", "BA2371": "F12C12Y48", "BA2372": "F0C0Y72", "BA2377": "F0C0Y76", "BA2378": "F12C0Y112", "BA2380": "F12C30Y117", "BA2381": "F16C0Y165", "BA2382": "F8C12Y126", "BA2383": "F12C0Y132", "BA2384": "F8C12Y99", "BA2385": "F12C12Y48", "BA2386": "F0C0Y72", "BA2387": "F0C0Y76", "BA2388": "F12C0Y112", "BA2389": "F12C30Y117", "BA2390": "F16C0Y165", "BA2391": "F8C12Y126", "BA2392": "F12C0Y132", "BA2393": "F8C12Y99", "BA2396": "F12C12Y48", "BA2397": "F0C0Y72", "BA2399": "F0C0Y76", "BA2400": "F12C0Y112", "BA2401": "F12C30Y117", "BA2403": "F16C0Y165", "BA2404": "F8C12Y126", "BA2405": "F12C0Y132", "BA2408": "F8C12Y99", "BA2409": "F12C12Y48", "BA2410": "F0C0Y72", "BA2411": "F0C0Y76", "BA2412": "F12C0Y112", "BA2420": "F12C30Y117", "BA2422": "F16C0Y165", "BA2426": "F8C12Y126", "BA2428": "F12C0Y132", "BA2429": "F8C12Y99", "BA2430": "F12C12Y48", "BA2433": "F0C0Y72", "BA2435": "F0C0Y76", "BA2438": "F12C0Y112", "BA2441": "F12C30Y117", "BA2442": "F16C0Y165", "BA2444": "F8C12Y126", "BA2445": "F12C0Y132", "BA2448": "F8C12Y99", "BA2450": "F12C12Y48", "BA2451": "F0C0Y72", "BA2452": "F0C0Y76", "BA2455": "F12C0Y112", "BA2459": "F12C30Y117", "BA2460": "F16C0Y165", "BA2461": "F8C12Y126", "BA2462": "F12C0Y132", "BA2467": "F8C12Y99", "BA2472": "F12C12Y48", "BA2473": "F0C0Y72", "BA2474": "F0C0Y76", "BA2475": "F12C0Y112", "BA2478": "F12C30Y117", "BA2479": "F16C0Y165", "BA2483": "F8C12Y126", "BA2484": "F12C0Y132", "BA2485": "F8C12Y99", "BA2486": "F12C12Y48", "BA2487": "F0C0Y72", "BA2488": "F0C0Y76", "BA2489": "F12C0Y112", "BA2490": "F12C30Y117", "BA2491": "F16C0Y165", "BA2492": "F8C12Y126", "BA2493": "F12C0Y132", "BA2494": "F8C12Y99", "BA2495": "F12C12Y48", "BA2500": "F0C0Y72", "BA2502": "F0C0Y76", "BA2506": "F12C0Y112", "BA2509": "F12C30Y117", "BA2513": "F16C0Y165", "BA2514": "F8C12Y126", "BA2517": "F12C0Y132", "BA2523": "F8C12Y99", "BA2526": "F12C12Y48", "BA2532": "F0C0Y72", "BA2533": "F0C0Y76", "BA2534": "F12C0Y112", "BA2537": "F12C30Y117", "BA2538": "F16C0Y165", "BA2539": "F8C12Y126", "BA2541": "F12C0Y132", "BA2542": "F8C12Y99", "BA2543": "F12C12Y48", "BA2544": "F0C0Y72", "BA2545": "F0C0Y76", "BA2546": "F12C0Y112", "BA2563": "F12C30Y117", "BA2565": "F16C0Y165", "BA2570": "F8C12Y126", "BA2571": "F12C0Y132", "BA2573": "F8C12Y99", "BA2574": "F12C12Y48", "BA2576": "F0C0Y72", "BA2579": "F0C0Y76", "BA2580": "F12C0Y112", "BA2583": "F12C30Y117", "BA2584": "F16C0Y165", "BA2589": "F8C12Y126", "BA2593": "F12C0Y132", "BA2594": "F8C12Y99", "BA2603": "F12C12Y48", "BA2604": "F0C0Y72", "BA2609": "F0C0Y76", "BA2610": "F12C0Y112", "BA2614": "F12C30Y117", "BA2617": "F16C0Y165", "BA2618": "F8C12Y126", "BA2624": "F12C0Y132", "BA2627": "F8C12Y99", "BA2629": "F12C12Y48", "BA2631": "F0C0Y72", "BA2632": "F0C0Y76", "BA2633": "F12C0Y112", "BA2634": "F12C30Y117", "BA2635": "F16C0Y165", "BA2636": "F8C12Y126", "BA2637": "F12C0Y132", "BA2638": "F8C12Y99", "BA2642": "F12C12Y48", "BA2644": "F0C0Y72", "BA2645": "F0C0Y76", "BA2670": "F12C0Y112", "BA2700": "F12C30Y117", "AA2701": "F16C0Y165", "AA2702": "F8C12Y126"}}, "comparisons": {"naive_vs_optimized": {"profit_improvement": -2745882.9833345283, "profit_improvement_pct": -48.13116535205133, "revenue_improvement": -8280336.0000012, "revenue_improvement_pct": -44.17357161910482, "cost_savings": 5534453.016666671, "cost_savings_pct": 42.44212436094073, "meets_target": false}, "pre_merger_vs_optimized": {"profit_improvement": -1840882.9833345283, "profit_improvement_pct": -38.351728819469336, "scale_improvement": 1.5395833333333333}}}, "operational": {"utilization": {"F8C12Y126": {"fleet_size": 39, "assigned_flights": 83, "utilization_rate": 2.128205128205128, "total_flight_hours": 182.36666666666653, "avg_hours_per_aircraft": 4.676068376068373, "efficiency_score": 100}, "F12C0Y132": {"fleet_size": 8, "assigned_flights": 82, "utilization_rate": 10.25, "total_flight_hours": 174.38333333333335, "avg_hours_per_aircraft": 21.79791666666667, "efficiency_score": 100}, "F8C12Y99": {"fleet_size": 9, "assigned_flights": 82, "utilization_rate": 9.11111111111111, "total_flight_hours": 189.21666666666673, "avg_hours_per_aircraft": 21.02407407407408, "efficiency_score": 100}, "F12C12Y48": {"fleet_size": 11, "assigned_flights": 82, "utilization_rate": 7.454545454545454, "total_flight_hours": 173.1666666666667, "avg_hours_per_aircraft": 15.742424242424248, "efficiency_score": 100}, "F0C0Y72": {"fleet_size": 4, "assigned_flights": 82, "utilization_rate": 20.5, "total_flight_hours": 195.19999999999993, "avg_hours_per_aircraft": 48.79999999999998, "efficiency_score": 100}, "F0C0Y76": {"fleet_size": 38, "assigned_flights": 82, "utilization_rate": 2.1578947368421053, "total_flight_hours": 170.14999999999998, "avg_hours_per_aircraft": 4.477631578947368, "efficiency_score": 100}, "F12C0Y112": {"fleet_size": 10, "assigned_flights": 82, "utilization_rate": 8.2, "total_flight_hours": 198.13333333333333, "avg_hours_per_aircraft": 19.813333333333333, "efficiency_score": 100}, "F12C30Y117": {"fleet_size": 44, "assigned_flights": 82, "utilization_rate": 1.8636363636363635, "total_flight_hours": 199.4499999999999, "avg_hours_per_aircraft": 4.532954545454543, "efficiency_score": 100}, "F16C0Y165": {"fleet_size": 48, "assigned_flights": 82, "utilization_rate": 1.7083333333333333, "total_flight_hours": 192.4333333333334, "avg_hours_per_aircraft": 4.009027777777779, "efficiency_score": 100}}, "load_factors": {"F8C12Y126": {"total_flights": 83, "total_demand": 7763.409999994001, "total_capacity": 10458, "load_factor": 0.742341747943584, "passenger_utilization": 74.2341747943584}, "F12C0Y132": {"total_flights": 82, "total_demand": 7817.3799999999965, "total_capacity": 10824, "load_factor": 0.7222265336289724, "passenger_utilization": 72.22265336289723}, "F8C12Y99": {"total_flights": 82, "total_demand": 7554.289999999999, "total_capacity": 8118, "load_factor": 0.9305604828775559, "passenger_utilization": 93.05604828775559}, "F12C12Y48": {"total_flights": 82, "total_demand": 7693.829999999998, "total_capacity": 3936, "load_factor": 1.9547332317073165, "passenger_utilization": 100}, "F0C0Y72": {"total_flights": 82, "total_demand": 7644.140000000003, "total_capacity": 5904, "load_factor": 1.2947391598915994, "passenger_utilization": 100}, "F0C0Y76": {"total_flights": 82, "total_demand": 7846.490000000001, "total_capacity": 6232, "load_factor": 1.2590645057766368, "passenger_utilization": 100}, "F12C0Y112": {"total_flights": 82, "total_demand": 8032.169999999997, "total_capacity": 9184, "load_factor": 0.8745829703832749, "passenger_utilization": 87.45829703832749}, "F12C30Y117": {"total_flights": 82, "total_demand": 7492.459999993999, "total_capacity": 9594, "load_factor": 0.7809526787569313, "passenger_utilization": 78.09526787569312}, "F16C0Y165": {"total_flights": 82, "total_demand": 8087.039999993997, "total_capacity": 13530, "load_factor": 0.5977117516625275, "passenger_utilization": 59.77117516625275}}, "turnaround_efficiency": {"avg_turnaround_time": 65, "min_turnaround_time": 40, "turnaround_compliance": 0.95, "efficient_turnarounds": 0.85}, "airport_performance": {"CFU": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "CMJ": {"departures": 9, "arrivals": 9, "total_flights": 18, "hub_score": 21.428571428571427}, "MHC": {"departures": 4, "arrivals": 4, "total_flights": 8, "hub_score": 9.523809523809524}, "TJU": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "TCB": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "JBI": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "PNF": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "IMO": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "CJM": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "SEN": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "ZZK": {"departures": 5, "arrivals": 5, "total_flights": 10, "hub_score": 11.904761904761903}, "NFY": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "CAO": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "TKD": {"departures": 20, "arrivals": 20, "total_flights": 40, "hub_score": 47.61904761904761}, "FVH": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "POU": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "MJI": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "EDB": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "SOP": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "NJB": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "HFH": {"departures": 23, "arrivals": 23, "total_flights": 46, "hub_score": 54.761904761904766}, "MBQ": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "QWS": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "MXT": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "QTH": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "TBO": {"departures": 14, "arrivals": 14, "total_flights": 28, "hub_score": 33.33333333333333}, "QTQ": {"departures": 4, "arrivals": 4, "total_flights": 8, "hub_score": 9.523809523809524}, "TOB": {"departures": 11, "arrivals": 11, "total_flights": 22, "hub_score": 26.190476190476193}, "NDJ": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "CPJ": {"departures": 16, "arrivals": 16, "total_flights": 32, "hub_score": 38.095238095238095}, "ZZD": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "PHH": {"departures": 7, "arrivals": 7, "total_flights": 14, "hub_score": 16.666666666666664}, "QIY": {"departures": 7, "arrivals": 7, "total_flights": 14, "hub_score": 16.666666666666664}, "MBY": {"departures": 37, "arrivals": 37, "total_flights": 74, "hub_score": 88.09523809523809}, "ZLN": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "NNI": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "DEW": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "HUG": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "LPB": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "UVT": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "PUA": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "EGX": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "CVS": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "TNG": {"departures": 10, "arrivals": 10, "total_flights": 20, "hub_score": 23.809523809523807}, "BOD": {"departures": 46, "arrivals": 46, "total_flights": 92, "hub_score": 109.52380952380953}, "NAU": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "GBU": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "LUO": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "TKE": {"departures": 4, "arrivals": 4, "total_flights": 8, "hub_score": 9.523809523809524}, "EFO": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "TDD": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "IOM": {"departures": 8, "arrivals": 8, "total_flights": 16, "hub_score": 19.047619047619047}, "PSE": {"departures": 5, "arrivals": 5, "total_flights": 10, "hub_score": 11.904761904761903}, "ZWS": {"departures": 13, "arrivals": 13, "total_flights": 26, "hub_score": 30.952380952380953}, "HEM": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "QVX": {"departures": 4, "arrivals": 4, "total_flights": 8, "hub_score": 9.523809523809524}, "QEY": {"departures": 97, "arrivals": 97, "total_flights": 194, "hub_score": 230.95238095238093}, "QTD": {"departures": 6, "arrivals": 6, "total_flights": 12, "hub_score": 14.285714285714285}, "XSH": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "GBJ": {"departures": 13, "arrivals": 13, "total_flights": 26, "hub_score": 30.952380952380953}, "NTQ": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "NTP": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "BER": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "TUM": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "FXS": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "CSX": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "ZBL": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "NDP": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "CPT": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "ZMX": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "FBU": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "TGP": {"departures": 16, "arrivals": 16, "total_flights": 32, "hub_score": 38.095238095238095}, "TFB": {"departures": 245, "arrivals": 245, "total_flights": 490, "hub_score": 583.3333333333333}, "BMX": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "EVU": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "MBT": {"departures": 15, "arrivals": 15, "total_flights": 30, "hub_score": 35.714285714285715}, "BUM": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "KOV": {"departures": 12, "arrivals": 12, "total_flights": 24, "hub_score": 28.57142857142857}, "ZFH": {"departures": 3, "arrivals": 3, "total_flights": 6, "hub_score": 7.142857142857142}, "TUT": {"departures": 5, "arrivals": 5, "total_flights": 10, "hub_score": 11.904761904761903}, "NGS": {"departures": 7, "arrivals": 7, "total_flights": 14, "hub_score": 16.666666666666664}, "BVT": {"departures": 1, "arrivals": 1, "total_flights": 2, "hub_score": 2.380952380952381}, "GDB": {"departures": 2, "arrivals": 2, "total_flights": 4, "hub_score": 4.761904761904762}, "PBL": {"departures": 13, "arrivals": 13, "total_flights": 26, "hub_score": 30.952380952380953}}, "kpis": {"avg_fleet_utilization": 7.041525125297055, "meets_utilization_target": true, "avg_load_factor": 1.0174347847364889, "meets_load_factor_target": true, "turnaround_compliance": 0.95, "meets_turnaround_target": true}}, "timestamp": "2025-07-30T16:07:28.070243"}, "memory_usage": {"before": {"memory_percent": 6.1, "memory_used_gb": 13.56, "memory_total_gb": 251.56}, "after": {"memory_percent": 6.1, "memory_used_gb": 13.57, "memory_total_gb": 251.56}}}, "reporting": {"status": "success", "duration_seconds": 0.45, "start_time": "2025-07-30T16:07:28.080055", "end_time": "2025-07-30T16:07:28.533355", "error_message": null, "message": "报告生成完成", "generated_files": [{"file": "fleet_assignment_report.md", "path": "results/reports/fleet_assignment_report.md", "size_kb": 7.89, "exists": true}, {"file": "fleet_assignment_presentation.pptx", "path": "results/reports/fleet_assignment_presentation.pptx", "size_kb": 35.89, "exists": true}], "report_generation_duration": 0.16, "ppt_generation_duration": 0.04, "memory_usage": {"before": {"memory_percent": 6.1, "memory_used_gb": 13.57, "memory_total_gb": 251.56}, "after": {"memory_percent": 6.1, "memory_used_gb": 13.61, "memory_total_gb": 251.56}}}}, "final_results": {"overall_status": "partial_success", "total_duration_seconds": 53.53, "successful_steps": ["data_preprocessing", "demand_prediction", "analysis", "reporting"], "failed_steps": ["fleet_optimization"], "completion_rate": "4/5", "best_model_performance": {"model": "XGBoost (最佳模型)", "r2_score": 0.9998912458594615, "mse": 0.2620922155164148, "mae": 0.3581522550356182, "rmse": 0.5119494267175371, "mape": 3961251.384462326}, "optimization_results": null}, "performance_metrics": {"total_duration": 53.53, "final_memory_usage": {"memory_percent": 6.1, "memory_used_gb": 13.61, "memory_total_gb": 251.56}, "peak_memory_usage": {"memory_percent": 6.1, "memory_used_gb": 13.61, "memory_total_gb": 251.56}, "steps_completed": 4, "steps_failed": 1}}