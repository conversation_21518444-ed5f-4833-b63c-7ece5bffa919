{"total_features": 20, "top_features": ["dep_minutes_utc", "arr_minutes_utc", "flight_duration", "fare", "class_encoded", "origin_encoded", "destination_encoded", "time_of_day_morning", "time_of_day_afternoon", "time_of_day_evening"], "analysis_method": "SHAP (Simulated)", "model_type": "XGBoost (Simulated)", "key_insights": ["Departure time is the most critical factor", "Route characteristics strongly influence demand", "Temporal patterns drive booking behavior", "Price sensitivity varies by time slot"], "recommendations": ["Dynamic pricing based on departure time", "Strategic fleet deployment to high-demand routes", "Targeted marketing for peak booking periods", "Capacity optimization for popular time slots"]}