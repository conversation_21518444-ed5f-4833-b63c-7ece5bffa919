#!/usr/bin/env python3
"""
测试增强版工作流执行脚本
验证日志收集和监控功能
"""

import sys
import os
from datetime import datetime

def test_enhanced_workflow():
    """测试增强版工作流"""
    print("=" * 60)
    print("测试增强版航空公司机型分配优化系统")
    print("=" * 60)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 检查必要的目录
        required_dirs = ['results', 'results/logs', 'models', 'data']
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ 创建目录: {dir_name}")
            else:
                print(f"✅ 目录存在: {dir_name}")
        
        # 检查必要的依赖包
        print("\n检查依赖包...")
        required_packages = ['psutil', 'pandas', 'numpy', 'logging']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} 未安装")
        
        if missing_packages:
            print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        # 导入并测试增强版工作流
        print("\n导入增强版工作流...")
        from execute_complete_workflow import (
            setup_logging, 
            collect_system_info, 
            log_step_start, 
            log_step_end,
            log_memory_usage
        )
        
        # 测试日志系统
        print("测试日志系统...")
        logger, log_file = setup_logging()
        print(f"✅ 日志文件创建: {log_file}")
        
        # 测试系统信息收集
        print("测试系统信息收集...")
        system_info = collect_system_info()
        print(f"✅ 系统信息: {system_info['platform']}")
        print(f"   Python版本: {system_info['python_version']}")
        print(f"   CPU核心数: {system_info['cpu_count']}")
        print(f"   总内存: {system_info['memory_total_gb']}GB")
        
        # 测试步骤日志记录
        print("测试步骤日志记录...")
        step_start = log_step_start(logger, "测试步骤", 1)
        import time
        time.sleep(1)  # 模拟工作
        step_result = log_step_end(logger, "测试步骤", step_start, True)
        print(f"✅ 步骤日志: 耗时 {step_result['duration_seconds']}秒")
        
        # 测试内存监控
        print("测试内存监控...")
        memory_info = log_memory_usage(logger, "测试内存监控")
        print(f"✅ 内存使用: {memory_info['memory_percent']}%")
        
        print("\n" + "=" * 60)
        print("🎉 增强版工作流测试成功!")
        print("=" * 60)
        print("现在可以运行完整的工作流:")
        print("python execute_complete_workflow.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_workflow()
    sys.exit(0 if success else 1)
